#!/bin/bash

# =============================================================================
# AiLabel平台统一Celery管理脚本
# Unified Celery Management Script for AiLabel Platform
# 
# 功能: 统一管理开发环境和生产环境的Celery服务
# 作者: AiLabel Team
# 版本: 1.0.0
# 日期: 2025-07-14
# =============================================================================

# 设置脚本在遇到错误时退出
set -e

# =============================================================================
# 颜色定义和日志函数
# =============================================================================

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    local env_tag="$1"
    local message="$2"
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] [$env_tag] INFO: $message${NC}"
}

log_success() {
    local env_tag="$1"
    local message="$2"
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] [$env_tag] SUCCESS: $message${NC}"
}

log_warning() {
    local env_tag="$1"
    local message="$2"
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] [$env_tag] WARNING: $message${NC}"
}

log_error() {
    local env_tag="$1"
    local message="$2"
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] [$env_tag] ERROR: $message${NC}"
}

log_header() {
    local message="$1"
    echo -e "${CYAN}===============================================================================${NC}"
    echo -e "${CYAN} $message${NC}"
    echo -e "${CYAN}===============================================================================${NC}"
}

# =============================================================================
# 帮助信息
# =============================================================================

show_help() {
    cat << EOF
${CYAN}AiLabel平台统一Celery管理脚本${NC}

${YELLOW}用法:${NC}
    $0 <环境> <操作> [选项]

${YELLOW}参数:${NC}
    环境 (必需):
        dev     开发环境
        prod    生产环境
    
    操作 (必需):
        start   启动Celery服务
        stop    停止Celery服务
        restart 重启Celery服务

${YELLOW}选项:${NC}
    --help, -h          显示此帮助信息
    --force             强制执行操作（跳过确认）
    --verbose           详细输出模式

${YELLOW}使用示例:${NC}
    $0 dev start        # 启动开发环境Celery
    $0 prod stop        # 停止生产环境Celery
    $0 dev restart      # 重启开发环境Celery
    $0 prod start --force   # 强制启动生产环境（跳过确认）

${YELLOW}环境配置:${NC}
    开发环境 (dev):
        - Conda环境: ailabel_env
        - 日志目录: log/dev
        - 配置文件: .env.dev
        - 进程标识: aiLabel_dev
    
    生产环境 (prod):
        - Conda环境: /home/<USER>/miniconda3/envs/myenv
        - 日志目录: log/prod
        - 配置文件: .env.prod
        - 进程标识: aiLabel_prod

${YELLOW}注意事项:${NC}
    1. 确保在项目根目录下执行此脚本
    2. 确保对应的环境配置文件存在
    3. 生产环境操作需要谨慎，建议先在开发环境测试
    4. 脚本会自动处理进程清理和环境隔离

EOF
}

# =============================================================================
# 环境配置函数
# =============================================================================

setup_environment_config() {
    local env="$1"
    
    case "$env" in
        "dev")
            ENV_TYPE="dev"
            ENV_TAG="DEV"
            PLATFORM_NAME="aiLabel"
            CONDA_ENV="ailabel_env"
            LOG_DIR="log/dev"
            ENV_FILE=".env.dev"
            WORKER_NAME="aiLabel_dev_worker@$(hostname)"
            HOSTNAME_PREFIX="aiLabel_dev_$(hostname)"
            PROCESS_PATTERN="celery.*$CONDA_ENV.*aiLabel_python_backend.*dev"
            SIMPLE_PATTERN="celery.*$CONDA_ENV.*dev"
            WAIT_TIME=10
            ;;
        "prod")
            ENV_TYPE="prod"
            ENV_TAG="PROD"
            PLATFORM_NAME="aiLabel"
            CONDA_ENV="/home/<USER>/miniconda3/envs/myenv"
            LOG_DIR="log/prod"
            ENV_FILE=".env.prod"
            WORKER_NAME="aiLabel_prod_worker@$(hostname)"
            HOSTNAME_PREFIX="aiLabel_prod_$(hostname)"
            PROCESS_PATTERN="celery.*myenv.*aiLabel_python_backend.*prod"
            SIMPLE_PATTERN="celery.*myenv.*prod"
            WAIT_TIME=15
            ;;
        *)
            log_error "SYSTEM" "无效的环境参数: $env (支持: dev, prod)"
            exit 1
            ;;
    esac
    
    # 验证环境配置文件
    if [ ! -f "$ENV_FILE" ]; then
        log_error "$ENV_TAG" "环境配置文件 $ENV_FILE 不存在"
        exit 1
    fi
}

# =============================================================================
# 通用工具函数
# =============================================================================

# 激活conda环境
activate_conda_env() {
    log_info "$ENV_TAG" "正在激活conda环境..."
    
    if [ -f ~/miniconda3/etc/profile.d/conda.sh ]; then
        source ~/miniconda3/etc/profile.d/conda.sh
        if conda activate "$CONDA_ENV"; then
            log_success "$ENV_TAG" "成功激活conda环境: $CONDA_ENV"
            return 0
        else
            log_error "$ENV_TAG" "激活conda环境失败"
            return 1
        fi
    else
        log_error "$ENV_TAG" "找不到conda配置文件"
        return 1
    fi
}

# 创建日志目录
create_log_directory() {
    log_info "$ENV_TAG" "创建日志目录..."
    
    if mkdir -p "$LOG_DIR"; then
        log_success "$ENV_TAG" "日志目录创建成功: $LOG_DIR"
        return 0
    else
        log_error "$ENV_TAG" "日志目录创建失败"
        return 1
    fi
}

# 获取进程数量
get_process_count() {
    pgrep -f "$SIMPLE_PATTERN" 2>/dev/null | wc -l
}

# 检查进程是否属于当前项目
is_project_process() {
    local pid="$1"
    local proc_cwd
    proc_cwd=$(readlink -f "/proc/$pid/cwd" 2>/dev/null || echo "")
    [[ "$proc_cwd" == *"aiLabel_python_backend"* ]]
}

# 检查进程是否属于当前用户
is_user_process() {
    local pid="$1"
    local current_user
    local proc_user
    current_user=$(whoami)
    proc_user=$(ps -o user= -p "$pid" 2>/dev/null || echo "")
    [ "$proc_user" = "$current_user" ]
}

# =============================================================================
# 进程清理函数
# =============================================================================

cleanup_existing_processes() {
    log_info "$ENV_TAG" "正在清除已存在的${ENV_TYPE}环境Celery进程..."

    local current_user
    current_user=$(whoami)

    log_info "$ENV_TAG" "进程识别模式: $PROCESS_PATTERN"

    # 查找现有进程
    local existing_processes
    existing_processes=$(pgrep -f "$SIMPLE_PATTERN" 2>/dev/null || true)

    if [ -n "$existing_processes" ]; then
        log_warning "$ENV_TAG" "发现${ENV_TYPE}环境Celery进程，正在清理..."

        for pid in $existing_processes; do
            if is_user_process "$pid" && is_project_process "$pid"; then
                log_info "$ENV_TAG" "停止${ENV_TYPE}环境进程 PID: $pid"
                kill -TERM "$pid" 2>/dev/null || true
            fi
        done

        sleep 3

        # 检查是否还有残留进程
        local remaining_processes
        remaining_processes=$(pgrep -f "$SIMPLE_PATTERN" 2>/dev/null || true)
        if [ -n "$remaining_processes" ]; then
            log_warning "$ENV_TAG" "发现残留进程，强制清理..."
            for pid in $remaining_processes; do
                if is_user_process "$pid" && is_project_process "$pid"; then
                    log_info "$ENV_TAG" "强制终止进程 PID: $pid"
                    kill -KILL "$pid" 2>/dev/null || true
                fi
            done
            sleep 2
        fi

        log_success "$ENV_TAG" "进程清理完成"
    else
        log_info "$ENV_TAG" "没有发现运行中的${ENV_TYPE}环境Celery进程"
    fi
}

# =============================================================================
# 启动功能
# =============================================================================

start_celery() {
    log_header "启动${ENV_TYPE}环境Celery服务"

    log_info "$ENV_TAG" "开始启动标注平台${ENV_TYPE}环境Celery服务..."
    log_info "$ENV_TAG" "环境类型: ${ENV_TYPE}环境 ($ENV_TYPE)"
    log_info "$ENV_TAG" "平台标识: $PLATFORM_NAME"
    log_info "$ENV_TAG" "Conda环境: $CONDA_ENV"
    log_info "$ENV_TAG" "日志目录: $LOG_DIR"
    log_info "$ENV_TAG" "配置文件: $ENV_FILE"

    # 1. 激活conda环境
    if ! activate_conda_env; then
        exit 1
    fi

    # 2. 创建日志目录
    if ! create_log_directory; then
        exit 1
    fi

    # 3. 清除已存在的进程
    cleanup_existing_processes

    # 4. 管理日志文件
    log_info "$ENV_TAG" "管理日志文件..."
    if [ -f "$LOG_DIR/celery.log" ]; then
        local backup_file="$LOG_DIR/celery_$(date '+%Y%m%d_%H%M%S').log"
        mv "$LOG_DIR/celery.log" "$backup_file"
        log_info "$ENV_TAG" "已备份当前日志文件: $backup_file"
    fi

    # 创建新的日志文件并添加会话标记
    {
        echo "🚀 CELERY SESSION STARTED - $(date '+%Y-%m-%d %H:%M:%S')"
        echo "   Environment: ${ENV_TYPE}环境 (${ENV_TYPE^})"
        echo "   Started by: $(whoami)"
        echo "   Conda Environment: $CONDA_ENV"
        echo "   Worker Name: $WORKER_NAME"
        echo "   Hostname: $HOSTNAME_PREFIX"
        echo "   Configuration: $ENV_FILE"
        echo "================================================================================================"
        echo ""
    } > "$LOG_DIR/celery.log"

    log_success "$ENV_TAG" "已创建新的日志文件: $LOG_DIR/celery.log (包含会话标记)"

    # 5. 启动Celery Worker
    log_info "$ENV_TAG" "正在启动新的标注平台${ENV_TYPE}环境Celery Worker..."
    log_info "$ENV_TAG" "Worker名称: $WORKER_NAME"
    log_info "$ENV_TAG" "主机名前缀: $HOSTNAME_PREFIX"

    # 设置环境变量
    export CELERY_ENV="$ENV_TYPE"

    # 启动Celery命令
    nohup celery -A src.celery worker \
        --loglevel=INFO \
        --hostname="$HOSTNAME_PREFIX" \
        --logfile="$LOG_DIR/celery.log" \
        --pidfile="$LOG_DIR/celery.pid" \
        --time-limit=2400 \
        --soft-time-limit=1800 \
        --concurrency=10 \
        -n "$WORKER_NAME" \
        --queues=ailabel_queue \
        --pool=prefork \
        --max-tasks-per-child=50 \
        --without-gossip \
        --without-mingle \
        --without-heartbeat \
        > /dev/null 2>&1 &

    # 6. 等待启动
    log_info "$ENV_TAG" "等待Celery启动..."
    sleep "$WAIT_TIME"

    # 7. 验证启动状态
    log_info "$ENV_TAG" "验证Celery启动状态..."
    if [ -f "$LOG_DIR/celery.pid" ]; then
        local celery_pid
        celery_pid=$(cat "$LOG_DIR/celery.pid")
        if kill -0 "$celery_pid" 2>/dev/null; then
            log_success "$ENV_TAG" "标注平台${ENV_TYPE}环境Celery Worker启动成功! PID: $celery_pid"

            # 显示启动信息
            echo ""
            log_info "$ENV_TAG" "日志文件位置: $LOG_DIR/celery.log"
            log_info "$ENV_TAG" "PID文件位置: $LOG_DIR/celery.pid"
            log_info "$ENV_TAG" "环境配置: $ENV_FILE"
            echo ""
            log_info "$ENV_TAG" "可以使用以下命令查看日志:"
            echo "  tail -f $LOG_DIR/celery.log          # 查看实时日志"
            echo ""
            log_info "$ENV_TAG" "可以使用以下命令停止Celery:"
            echo "  $0 $ENV_TYPE stop                     # 使用管理脚本停止"
            echo "  kill $celery_pid                     # 直接终止进程"

            log_success "$ENV_TAG" "标注平台${ENV_TYPE}环境Celery启动脚本执行完成!"
            return 0
        else
            log_error "$ENV_TAG" "Celery进程启动失败"
            return 1
        fi
    else
        log_error "$ENV_TAG" "未找到PID文件，启动失败"
        return 1
    fi
}

# =============================================================================
# 停止功能
# =============================================================================

stop_celery() {
    log_header "停止${ENV_TYPE}环境Celery服务"

    log_info "$ENV_TAG" "开始停止标注平台${ENV_TYPE}环境Celery服务..."
    log_info "$ENV_TAG" "环境类型: ${ENV_TYPE}环境 ($ENV_TYPE)"
    log_info "$ENV_TAG" "日志目录: $LOG_DIR"

    # 1. 从PID文件停止进程
    if [ -f "$LOG_DIR/celery.pid" ]; then
        local celery_pid
        celery_pid=$(cat "$LOG_DIR/celery.pid")
        log_info "$ENV_TAG" "从PID文件读取到进程ID: $celery_pid"

        if kill -0 "$celery_pid" 2>/dev/null; then
            log_info "$ENV_TAG" "正在停止${ENV_TYPE}环境Celery进程 (PID: $celery_pid)..."
            kill -TERM "$celery_pid" 2>/dev/null || true

            # 等待进程优雅退出
            for i in $(seq 1 "$WAIT_TIME"); do
                if ! kill -0 "$celery_pid" 2>/dev/null; then
                    log_success "$ENV_TAG" "${ENV_TYPE}环境Celery进程已优雅退出"
                    break
                fi
                log_info "$ENV_TAG" "等待进程退出... ($i/$WAIT_TIME)"
                sleep 1
            done

            # 如果进程仍然存在，强制终止
            if kill -0 "$celery_pid" 2>/dev/null; then
                log_warning "$ENV_TAG" "进程未能优雅退出，强制终止..."
                kill -KILL "$celery_pid" 2>/dev/null || true
                sleep 2

                if kill -0 "$celery_pid" 2>/dev/null; then
                    log_error "$ENV_TAG" "无法终止进程 $celery_pid"
                else
                    log_success "$ENV_TAG" "已强制终止${ENV_TYPE}环境Celery进程"
                fi
            fi
        else
            log_warning "$ENV_TAG" "PID文件中的进程不存在，可能已经停止"
        fi

        # 添加会话结束标记到日志文件
        if [ -f "$LOG_DIR/celery.log" ]; then
            {
                echo ""
                echo "🛑 CELERY SESSION ENDED - $(date '+%Y-%m-%d %H:%M:%S')"
                echo "   Session terminated by: $(whoami)"
                echo "   Process ID: $celery_pid"
                echo "   Environment: ${ENV_TYPE}环境 (${ENV_TYPE^})"
                echo "================================================================================================"
                echo ""
            } >> "$LOG_DIR/celery.log"
        fi

        # 清理PID文件
        rm -f "$LOG_DIR/celery.pid"
        log_info "$ENV_TAG" "已清理PID文件"
    else
        log_warning "$ENV_TAG" "未找到PID文件: $LOG_DIR/celery.pid"
    fi

    # 2. 查找并清理所有相关的celery进程
    log_info "$ENV_TAG" "查找并清理所有${ENV_TYPE}环境Celery进程..."

    local current_user
    current_user=$(whoami)

    local existing_processes
    existing_processes=$(pgrep -f "$SIMPLE_PATTERN" 2>/dev/null || true)
    if [ -n "$existing_processes" ]; then
        log_warning "$ENV_TAG" "发现残留的${ENV_TYPE}环境Celery进程:"
        ps aux | grep "$SIMPLE_PATTERN" | grep -v grep || true

        for pid in $existing_processes; do
            if is_user_process "$pid" && is_project_process "$pid"; then
                log_info "$ENV_TAG" "清理${ENV_TYPE}环境进程 PID: $pid"
                kill -TERM "$pid" 2>/dev/null || true
            fi
        done

        sleep 3

        # 强制清理残留进程
        local remaining_processes
        remaining_processes=$(pgrep -f "$SIMPLE_PATTERN" 2>/dev/null || true)
        if [ -n "$remaining_processes" ]; then
            for pid in $remaining_processes; do
                if is_user_process "$pid" && is_project_process "$pid"; then
                    log_warning "$ENV_TAG" "强制终止残留进程 PID: $pid"
                    kill -KILL "$pid" 2>/dev/null || true
                fi
            done
        fi

        log_success "$ENV_TAG" "所有${ENV_TYPE}环境Celery进程已清理完成"
    else
        log_info "$ENV_TAG" "没有发现运行中的${ENV_TYPE}环境Celery进程"
    fi

    # 3. 显示其他环境的进程信息（如果存在）
    local other_env_pattern
    if [ "$ENV_TYPE" = "dev" ]; then
        other_env_pattern="celery.*myenv.*prod"
    else
        other_env_pattern="celery.*ailabel_env.*dev"
    fi

    local other_processes
    other_processes=$(pgrep -f "$other_env_pattern" 2>/dev/null || true)
    if [ -n "$other_processes" ]; then
        local other_env_name
        if [ "$ENV_TYPE" = "dev" ]; then
            other_env_name="生产环境"
        else
            other_env_name="开发环境"
        fi

        log_info "$ENV_TAG" "系统中仍有其他${other_env_name}Celery进程在运行:"
        ps aux | grep "$other_env_pattern" | grep -v grep || true
        log_info "$ENV_TAG" "这些进程不受影响，继续运行"
    fi

    log_success "$ENV_TAG" "标注平台${ENV_TYPE}环境Celery停止脚本执行完成!"
}

# =============================================================================
# 重启功能
# =============================================================================

restart_celery() {
    log_header "重启${ENV_TYPE}环境Celery服务"

    log_info "$ENV_TAG" "开始重启标注平台${ENV_TYPE}环境Celery服务..."

    # 先停止
    if stop_celery; then
        log_info "$ENV_TAG" "停止操作完成，等待3秒后启动..."
        sleep 3

        # 再启动
        if start_celery; then
            log_success "$ENV_TAG" "重启操作完成!"
            return 0
        else
            log_error "$ENV_TAG" "启动失败，重启操作未完成"
            return 1
        fi
    else
        log_error "$ENV_TAG" "停止失败，重启操作中止"
        return 1
    fi
}

# =============================================================================
# 参数解析和主函数
# =============================================================================

# 参数验证
validate_arguments() {
    if [ $# -lt 2 ]; then
        log_error "SYSTEM" "参数不足"
        echo ""
        show_help
        exit 1
    fi

    local env="$1"
    local action="$2"

    # 验证环境参数
    if [[ "$env" != "dev" && "$env" != "prod" ]]; then
        log_error "SYSTEM" "无效的环境参数: $env (支持: dev, prod)"
        exit 1
    fi

    # 验证操作参数
    if [[ "$action" != "start" && "$action" != "stop" && "$action" != "restart" ]]; then
        log_error "SYSTEM" "无效的操作参数: $action (支持: start, stop, restart)"
        exit 1
    fi
}

# 生产环境确认
confirm_production_operation() {
    local action="$1"

    if [ "$ENV_TYPE" = "prod" ] && [ "$FORCE_MODE" != "true" ]; then
        echo ""
        log_warning "PROD" "您即将在生产环境执行 $action 操作"
        log_warning "PROD" "这可能会影响正在运行的生产任务"
        echo ""
        read -p "确认继续? (y/N): " -r
        echo ""

        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "PROD" "操作已取消"
            exit 0
        fi
    fi
}

# 主函数
main() {
    # 解析命令行参数
    local env=""
    local action=""
    local force_mode=false
    local verbose_mode=false

    # 处理帮助参数
    for arg in "$@"; do
        case $arg in
            --help|-h)
                show_help
                exit 0
                ;;
        esac
    done

    # 验证参数数量
    validate_arguments "$@"

    env="$1"
    action="$2"
    shift 2

    # 处理其他选项
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force)
                force_mode=true
                shift
                ;;
            --verbose)
                verbose_mode=true
                shift
                ;;
            *)
                log_error "SYSTEM" "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 设置全局变量
    FORCE_MODE="$force_mode"
    VERBOSE_MODE="$verbose_mode"

    # 设置环境配置
    setup_environment_config "$env"

    # 显示操作信息
    log_header "AiLabel平台Celery管理脚本"
    log_info "SYSTEM" "环境: $ENV_TYPE"
    log_info "SYSTEM" "操作: $action"
    log_info "SYSTEM" "强制模式: $force_mode"
    log_info "SYSTEM" "详细模式: $verbose_mode"
    echo ""

    # 生产环境确认
    confirm_production_operation "$action"

    # 执行操作
    case "$action" in
        "start")
            start_celery
            ;;
        "stop")
            stop_celery
            ;;
        "restart")
            restart_celery
            ;;
        *)
            log_error "SYSTEM" "未知操作: $action"
            exit 1
            ;;
    esac
}

# =============================================================================
# 脚本入口
# =============================================================================

# 错误处理
trap 'log_error "SYSTEM" "脚本执行被中断"; exit 130' INT TERM

# 检查是否在项目根目录
if [ ! -f "src/__init__.py" ] || [ ! -d "scripts/celery" ]; then
    log_error "SYSTEM" "请在项目根目录下执行此脚本"
    exit 1
fi

# 执行主函数
main "$@"
