#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Celery任务测试脚本
直接使用Celery客户端发送任务
"""

import os
import sys
import time
from datetime import datetime
from dotenv import load_dotenv

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

# 加载环境配置
load_dotenv(os.path.join(project_root, '.env.dev'))

# 设置环境变量
os.environ['CELERY_ENV'] = 'dev'

try:
    # 导入应用
    from src import celery
    
    def test_celery_connection():
        """测试Celery连接"""
        print("🔍 测试Celery连接...")
        
        try:
            # 检查Celery状态
            inspect = celery.control.inspect()
            stats = inspect.stats()
            
            if stats:
                print("✅ Celery连接正常")
                print(f"📋 活跃Worker数量: {len(stats)}")
                for worker_name, worker_stats in stats.items():
                    print(f"   - Worker: {worker_name}")
                    print(f"     进程数: {worker_stats.get('pool', {}).get('max-concurrency', 'N/A')}")
                return True
            else:
                print("❌ 没有发现活跃的Worker")
                return False
                
        except Exception as e:
            print(f"❌ Celery连接测试失败: {e}")
            return False
    
    def create_test_task():
        """创建一个简单的测试任务"""
        @celery.task(name='test.simple_task')
        def simple_task(message, number):
            """简单的测试任务"""
            print(f"🎯 处理测试任务: {message}, 数字: {number}")
            time.sleep(2)  # 模拟处理时间
            result = f"任务完成: {message} - {number} - {datetime.now()}"
            print(f"✅ 任务结果: {result}")
            return result
        
        return simple_task
    
    def send_test_tasks():
        """发送测试任务"""
        print("🚀 开始发送Celery测试任务...")
        
        # 创建测试任务
        test_task = create_test_task()
        
        try:
            # 发送单个任务
            print("\n📤 发送单个测试任务...")
            result = test_task.delay("Hello Celery", 42)
            print(f"✅ 任务已发送，ID: {result.id}")
            
            # 发送多个任务
            print("\n📤 发送多个测试任务...")
            task_results = []
            for i in range(3):
                result = test_task.delay(f"批量任务 {i+1}", i+1)
                task_results.append(result)
                print(f"✅ 批量任务 {i+1} 已发送，ID: {result.id}")
                time.sleep(0.5)
            
            print(f"\n📋 总共发送了 {len(task_results) + 1} 个任务")
            print("\n🔍 请检查Celery日志查看处理结果:")
            print("   tail -f log/dev/celery.log")
            
            return True
            
        except Exception as e:
            print(f"❌ 发送任务失败: {e}")
            return False
    
    def send_image_processing_simulation():
        """发送模拟图像处理任务"""
        print("\n🖼️ 发送模拟图像处理任务...")
        
        @celery.task(name='test.image_process_simulation')
        def image_process_simulation(image_path, target_format):
            """模拟图像处理任务"""
            print(f"🎯 开始处理图像: {image_path}")
            print(f"📋 目标格式: {target_format}")
            
            # 模拟处理时间
            for i in range(3):
                print(f"   处理进度: {(i+1)*33}%")
                time.sleep(1)
            
            result = {
                "source": image_path,
                "target_format": target_format,
                "status": "completed",
                "processed_at": datetime.now().isoformat()
            }
            
            print(f"✅ 图像处理完成: {result}")
            return result
        
        try:
            # 发送图像处理任务
            result = image_process_simulation.delay(
                "/test/sample_image.jpg", 
                "png"
            )
            print(f"✅ 图像处理任务已发送，ID: {result.id}")
            return True
            
        except Exception as e:
            print(f"❌ 发送图像处理任务失败: {e}")
            return False
    
    def main():
        """主函数"""
        print("🎯 Celery任务测试脚本")
        print("=" * 50)
        
        # 测试连接
        if not test_celery_connection():
            print("❌ Celery连接失败，请确保Worker正在运行")
            return
        
        # 发送测试任务
        if send_test_tasks():
            print("\n⏳ 等待3秒...")
            time.sleep(3)
            
            # 发送图像处理模拟任务
            send_image_processing_simulation()
        
        print("\n🎉 测试完成!")
        print("\n💡 提示:")
        print("   1. 查看实时日志: tail -f log/dev/celery.log")
        print("   2. 如果看到任务处理信息，说明Celery工作正常")
        print("   3. 任务会在队列中按顺序执行")

    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"❌ 导入应用失败: {e}")
    print("💡 请确保:")
    print("   1. 已激活正确的conda环境")
    print("   2. 已安装所有依赖包")
    print("   3. 在项目根目录运行此脚本")
except Exception as e:
    print(f"❌ 脚本执行失败: {e}")
