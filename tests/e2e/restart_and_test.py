#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化Celery重启和端到端测试脚本

该脚本提供以下功能：
1. 停止并重启开发环境的Celery Worker进程
2. 验证Celery进程状态
3. 自动运行完整的端到端测试
4. 错误处理和状态验证
"""

import os
import sys
import time
import subprocess
import argparse
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)


class CeleryRestartManager:
    """Celery重启管理器"""
    
    def __init__(self, project_root: str, env: str = 'dev'):
        """
        初始化重启管理器
        
        Args:
            project_root: 项目根目录
            env: 环境类型 (dev/prod)
        """
        self.project_root = project_root
        self.env = env
        self.logger = self._setup_logger()
        
        # 脚本路径
        self.celery_manager = os.path.join(project_root, 'scripts/celery/celery_manager.sh')
        
        # 验证脚本存在
        self._validate_scripts()
    
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger("CeleryRestartManager")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def _validate_scripts(self):
        """验证Celery管理脚本是否存在"""
        if not os.path.exists(self.celery_manager):
            raise FileNotFoundError(f"Celery管理脚本不存在: {self.celery_manager}")

        self.logger.info(f"✅ Celery管理脚本验证通过 ({self.env}环境)")
    
    def _run_command(self, command: str, timeout: int = 60) -> Tuple[bool, str, str]:
        """
        运行系统命令
        
        Args:
            command: 要执行的命令
            timeout: 超时时间（秒）
            
        Returns:
            Tuple[bool, str, str]: (成功状态, 标准输出, 错误输出)
        """
        try:
            self.logger.info(f"🔧 执行命令: {command}")
            
            result = subprocess.run(
                command,
                shell=True,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            success = result.returncode == 0
            stdout = result.stdout.strip()
            stderr = result.stderr.strip()
            
            if success:
                self.logger.info(f"✅ 命令执行成功")
            else:
                self.logger.error(f"❌ 命令执行失败 (返回码: {result.returncode})")
                if stderr:
                    self.logger.error(f"错误输出: {stderr}")
            
            return success, stdout, stderr
            
        except subprocess.TimeoutExpired:
            self.logger.error(f"❌ 命令执行超时 ({timeout}秒)")
            return False, "", "命令执行超时"
        except Exception as e:
            self.logger.error(f"❌ 命令执行异常: {e}")
            return False, "", str(e)
    
    def get_celery_processes(self) -> List[Dict[str, str]]:
        """
        获取当前运行的Celery进程
        
        Returns:
            List[Dict]: Celery进程信息列表
        """
        success, stdout, stderr = self._run_command(f'ps aux | grep "aiLabel_{self.env}" | grep -v grep')
        
        processes = []
        if success and stdout:
            for line in stdout.split('\n'):
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 2:
                        processes.append({
                            'user': parts[0],
                            'pid': parts[1],
                            'command': ' '.join(parts[10:]) if len(parts) > 10 else line
                        })
        
        return processes
    
    def stop_celery(self) -> bool:
        """
        停止Celery Worker进程
        
        Returns:
            bool: 停止是否成功
        """
        self.logger.info(f"🛑 开始停止{self.env}环境Celery Worker...")
        
        # 获取停止前的进程信息
        before_processes = self.get_celery_processes()
        self.logger.info(f"📋 停止前发现 {len(before_processes)} 个Celery进程")
        
        # 执行停止命令
        success, stdout, stderr = self._run_command(f'bash {self.celery_manager} {self.env} stop', timeout=120)
        
        if not success:
            self.logger.error(f"❌ 停止命令执行失败")
            return False
        
        # 等待进程完全停止
        self.logger.info("⏳ 等待进程完全停止...")
        time.sleep(5)
        
        # 检查是否还有残留进程
        after_processes = self.get_celery_processes()
        
        if after_processes:
            self.logger.warning(f"⚠️ 发现 {len(after_processes)} 个残留进程，尝试强制终止...")
            
            # 强制终止残留进程
            for process in after_processes:
                pid = process['pid']
                self.logger.info(f"🔧 强制终止进程 PID: {pid}")
                kill_success, _, _ = self._run_command(f'kill -9 {pid}')
                if kill_success:
                    self.logger.info(f"✅ 进程 {pid} 已终止")
                else:
                    self.logger.warning(f"⚠️ 无法终止进程 {pid}")
            
            # 再次检查
            time.sleep(2)
            final_processes = self.get_celery_processes()
            if final_processes:
                self.logger.error(f"❌ 仍有 {len(final_processes)} 个进程无法终止")
                return False
        
        self.logger.info("✅ Celery Worker停止成功")
        return True
    
    def start_celery(self) -> bool:
        """
        启动Celery Worker进程
        
        Returns:
            bool: 启动是否成功
        """
        self.logger.info(f"🚀 开始启动{self.env}环境Celery Worker...")
        
        # 执行启动命令
        success, stdout, stderr = self._run_command(f'bash {self.celery_manager} {self.env} start', timeout=120)

        if not success:
            self.logger.error(f"❌ 启动命令执行失败")
            return False
        
        # 等待进程启动
        self.logger.info("⏳ 等待进程启动...")
        time.sleep(10)
        
        # 验证进程是否启动成功
        processes = self.get_celery_processes()
        
        if not processes:
            self.logger.error("❌ 未发现Celery进程，启动失败")
            return False
        
        self.logger.info(f"✅ Celery Worker启动成功，发现 {len(processes)} 个进程")
        for process in processes:
            self.logger.info(f"   📋 PID: {process['pid']}")
        
        return True
    
    def restart_celery(self) -> bool:
        """
        重启Celery Worker进程
        
        Returns:
            bool: 重启是否成功
        """
        self.logger.info("🔄 开始重启Celery Worker...")
        
        # 停止Celery
        if not self.stop_celery():
            return False
        
        # 启动Celery
        if not self.start_celery():
            return False
        
        self.logger.info("✅ Celery Worker重启成功")
        return True


class E2ETestRunner:
    """端到端测试运行器"""

    def __init__(self, project_root: str):
        """
        初始化测试运行器

        Args:
            project_root: 项目根目录
        """
        self.project_root = project_root
        self.logger = self._setup_logger()

    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger("E2ETestRunner")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def run_e2e_test(self, env: str = 'dev', **kwargs) -> bool:
        """
        运行端到端测试

        Args:
            env: 环境类型
            **kwargs: 传递给测试脚本的其他参数

        Returns:
            bool: 测试是否成功
        """
        self.logger.info(f"🧪 开始运行端到端测试 ({env}环境)...")

        # 构建测试命令
        test_script = os.path.join(self.project_root, 'tests/e2e/run_tests.py')
        command_parts = [
            'PYTHONPATH=' + self.project_root,
            'python',
            test_script,
            '--env', env
        ]

        # 添加其他参数
        for key, value in kwargs.items():
            if key == 'batch_count' and value:
                command_parts.extend(['--batch-count', str(value)])
            elif key == 'image_type' and value:
                command_parts.extend(['--image-type', str(value)])
            elif key == 'project_id' and value:
                command_parts.extend(['--project-id', str(value)])
            elif key == 'no_batch' and value:
                command_parts.append('--no-batch')
            elif key == 'validate_only' and value:
                command_parts.append('--validate-only')
            elif key == 'single_task' and value:
                command_parts.append('--single-task')
            elif key == 'batch_only' and value:
                command_parts.append('--batch-only')
            elif key == 'quiet' and value:
                command_parts.append('--quiet')
            elif key == 'verbose' and value:
                command_parts.append('--verbose')

        command = ' '.join(command_parts)

        try:
            self.logger.info(f"🔧 执行测试命令: {command}")

            result = subprocess.run(
                command,
                shell=True,
                cwd=self.project_root,
                timeout=600  # 10分钟超时
            )

            success = result.returncode == 0

            if success:
                self.logger.info("✅ 端到端测试执行成功")
            else:
                self.logger.error(f"❌ 端到端测试执行失败 (返回码: {result.returncode})")

            return success

        except subprocess.TimeoutExpired:
            self.logger.error("❌ 端到端测试执行超时")
            return False
        except Exception as e:
            self.logger.error(f"❌ 端到端测试执行异常: {e}")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='自动化Celery重启和端到端测试工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s                              # 重启并运行完整测试 (开发环境)
  %(prog)s --env prod                   # 重启并运行完整测试 (生产环境)
  %(prog)s --no-restart                 # 仅运行测试，不重启Celery
  %(prog)s --restart-only               # 仅重启Celery，不运行测试
  %(prog)s --batch-count 5              # 重启并运行批量测试 (5个任务)
  %(prog)s --validate-only              # 重启并仅运行系统验证
        """
    )

    # 环境配置
    parser.add_argument('--env', choices=['dev', 'prod'], default='dev',
                       help='环境类型 (默认: dev)')

    # 操作控制
    parser.add_argument('--no-restart', action='store_true',
                       help='跳过Celery重启，仅运行测试')
    parser.add_argument('--restart-only', action='store_true',
                       help='仅重启Celery，不运行测试')

    # 测试参数 (传递给run_tests.py)
    parser.add_argument('--validate-only', action='store_true',
                       help='仅运行系统验证测试')
    parser.add_argument('--single-task', action='store_true',
                       help='仅运行单任务处理测试')
    parser.add_argument('--batch-only', action='store_true',
                       help='仅运行批量任务测试')
    parser.add_argument('--no-batch', action='store_true',
                       help='跳过批量任务测试')
    parser.add_argument('--image-type', type=int, choices=[1, 2, 3, 4],
                       help='图像类型ID (1=普通, 2=DICOM, 3=病理, 4=多通道)')
    parser.add_argument('--project-id', help='项目ID')
    parser.add_argument('--batch-count', type=int, help='批量任务数量')

    # 输出控制
    parser.add_argument('--quiet', action='store_true', help='减少输出信息')
    parser.add_argument('--verbose', action='store_true', help='显示详细输出')

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.basicConfig(level=logging.DEBUG)
    elif args.quiet:
        logging.basicConfig(level=logging.WARNING)
    else:
        logging.basicConfig(level=logging.INFO)

    # 获取项目根目录
    current_project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))

    success = True

    try:
        # 显示开始信息
        if not args.quiet:
            print("🎯 自动化Celery重启和端到端测试工具")
            print("=" * 60)
            print(f"环境: {args.env}")
            print(f"操作: {'仅重启' if args.restart_only else '仅测试' if args.no_restart else '重启+测试'}")
            print("=" * 60)

        # 步骤1: 重启Celery (如果需要)
        if not args.no_restart:
            restart_manager = CeleryRestartManager(current_project_root, args.env)

            if not restart_manager.restart_celery():
                print("❌ Celery重启失败，终止执行")
                return 1

            if args.restart_only:
                print("✅ Celery重启完成")
                return 0

        # 步骤2: 运行端到端测试 (如果需要)
        if not args.restart_only:
            test_runner = E2ETestRunner(current_project_root)

            # 准备测试参数
            test_kwargs = {}
            for attr in ['batch_count', 'image_type', 'project_id', 'no_batch',
                        'validate_only', 'single_task', 'batch_only', 'quiet', 'verbose']:
                value = getattr(args, attr)
                if value is not None:
                    test_kwargs[attr] = value

            if not test_runner.run_e2e_test(args.env, **test_kwargs):
                print("❌ 端到端测试失败")
                return 1

        print("✅ 所有操作完成成功")
        return 0

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        return 130
    except Exception as e:
        print(f"❌ 执行过程中发生异常: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
