#!/bin/bash
# -*- coding: utf-8 -*-
#
# 自动化Celery重启和端到端测试脚本 (Shell版本)
#
# 该脚本提供以下功能：
# 1. 停止并重启开发环境的Celery Worker进程
# 2. 验证Celery进程状态
# 3. 自动运行完整的端到端测试
# 4. 错误处理和状态验证
#

set -e  # 遇到错误立即退出

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
ENV="${1:-dev}"  # 默认开发环境

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
自动化Celery重启和端到端测试工具 (Shell版本)

用法: $0 [环境] [选项]

环境:
  dev     开发环境 (默认)
  prod    生产环境

选项:
  --help              显示此帮助信息
  --restart-only      仅重启Celery，不运行测试
  --test-only         仅运行测试，不重启Celery
  --batch-count N     设置批量测试任务数量 (默认: 3)
  --test-all-types    测试所有图像类型 (1=普通, 3=病理, 4=多通道)
  --image-type N      指定图像类型ID (默认: 3=病理)

示例:
  $0                      # 重启开发环境Celery并运行完整测试
  $0 prod                 # 重启生产环境Celery并运行完整测试
  $0 dev --restart-only   # 仅重启开发环境Celery
  $0 dev --test-only      # 仅运行开发环境测试
  $0 dev --batch-count 5  # 重启并运行5个批量任务测试
  $0 dev --test-all-types # 测试所有图像类型
  $0 dev --image-type 1   # 测试普通图像类型

EOF
}

# 解析命令行参数
RESTART_ONLY=false
TEST_ONLY=false
BATCH_COUNT=3
TEST_ALL_TYPES=false
IMAGE_TYPE=3

while [[ $# -gt 0 ]]; do
    case $1 in
        --help|-h)
            show_help
            exit 0
            ;;
        --restart-only)
            RESTART_ONLY=true
            shift
            ;;
        --test-only)
            TEST_ONLY=true
            shift
            ;;
        --batch-count)
            BATCH_COUNT="$2"
            shift 2
            ;;
        --test-all-types)
            TEST_ALL_TYPES=true
            shift
            ;;
        --image-type)
            IMAGE_TYPE="$2"
            shift 2
            ;;
        dev|prod)
            ENV="$1"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证环境参数
if [[ "$ENV" != "dev" && "$ENV" != "prod" ]]; then
    log_error "无效的环境参数: $ENV (支持: dev, prod)"
    exit 1
fi

# 验证项目结构
if [[ ! -d "$PROJECT_ROOT/scripts/celery" ]]; then
    log_error "项目结构验证失败: 未找到 scripts/celery 目录"
    exit 1
fi

CELERY_MANAGER="$PROJECT_ROOT/scripts/celery/celery_manager.sh"
TEST_SCRIPT="$PROJECT_ROOT/tests/e2e/run_tests.py"

# 验证脚本存在
if [[ ! -f "$CELERY_MANAGER" ]]; then
    log_error "Celery管理脚本不存在: $CELERY_MANAGER"
    exit 1
fi

if [[ ! -f "$TEST_SCRIPT" ]]; then
    log_error "测试脚本不存在: $TEST_SCRIPT"
    exit 1
fi

# 获取Celery进程数量
get_celery_process_count() {
    ps aux | grep "aiLabel_${ENV}" | grep -v grep | wc -l
}

# 等待进程状态变化
wait_for_process_change() {
    local expected_count=$1
    local timeout=${2:-30}
    local count=0
    
    while [[ $count -lt $timeout ]]; do
        local current_count=$(get_celery_process_count)
        if [[ $current_count -eq $expected_count ]]; then
            return 0
        fi
        sleep 1
        ((count++))
    done
    
    return 1
}

# 停止Celery
stop_celery() {
    log_info "🛑 开始停止${ENV}环境Celery Worker..."
    
    local before_count=$(get_celery_process_count)
    log_info "📋 停止前发现 $before_count 个Celery进程"
    
    # 执行停止命令
    if ! bash "$CELERY_MANAGER" "$ENV" stop; then
        log_error "停止命令执行失败"
        return 1
    fi
    
    # 等待进程停止
    log_info "⏳ 等待进程完全停止..."
    if wait_for_process_change 0 30; then
        log_success "Celery Worker停止成功"
        return 0
    else
        log_warning "部分进程可能未完全停止，尝试强制终止..."
        
        # 强制终止残留进程
        local pids=$(ps aux | grep "aiLabel_${ENV}" | grep -v grep | awk '{print $2}')
        if [[ -n "$pids" ]]; then
            echo "$pids" | xargs -r kill -9
            sleep 2
            
            local final_count=$(get_celery_process_count)
            if [[ $final_count -eq 0 ]]; then
                log_success "强制终止成功，Celery Worker已停止"
                return 0
            else
                log_error "仍有 $final_count 个进程无法终止"
                return 1
            fi
        else
            log_success "Celery Worker停止成功"
            return 0
        fi
    fi
}

# 启动Celery
start_celery() {
    log_info "🚀 开始启动${ENV}环境Celery Worker..."
    
    # 执行启动命令
    if ! bash "$CELERY_MANAGER" "$ENV" start; then
        log_error "启动命令执行失败"
        return 1
    fi
    
    # 等待进程启动
    log_info "⏳ 等待进程启动..."
    sleep 10
    
    local process_count=$(get_celery_process_count)
    if [[ $process_count -gt 0 ]]; then
        log_success "Celery Worker启动成功，发现 $process_count 个进程"
        return 0
    else
        log_error "未发现Celery进程，启动失败"
        return 1
    fi
}

# 重启Celery
restart_celery() {
    log_info "🔄 开始重启Celery Worker..."
    
    if ! stop_celery; then
        return 1
    fi
    
    if ! start_celery; then
        return 1
    fi
    
    log_success "Celery Worker重启成功"
    return 0
}

# 运行端到端测试
run_e2e_test() {
    log_info "🧪 开始运行端到端测试 (${ENV}环境)..."

    cd "$PROJECT_ROOT"

    # 构建测试命令
    local test_cmd="PYTHONPATH=\"$PROJECT_ROOT\" python \"$TEST_SCRIPT\" --env \"$ENV\" --batch-count \"$BATCH_COUNT\""

    # 添加图像类型参数
    if $TEST_ALL_TYPES; then
        test_cmd="$test_cmd --test-all-types"
        log_info "📋 将测试所有图像类型 (1=普通, 3=病理, 4=多通道)"
    else
        test_cmd="$test_cmd --image-type \"$IMAGE_TYPE\""
        log_info "📋 将测试图像类型: $IMAGE_TYPE"
    fi

    # 执行测试
    if eval "$test_cmd"; then
        log_success "端到端测试执行成功"
        return 0
    else
        log_error "端到端测试执行失败"
        return 1
    fi
}

# 主执行逻辑
main() {
    echo "🎯 自动化Celery重启和端到端测试工具"
    echo "============================================================"
    echo "环境: $ENV"
    echo "操作: $(if $RESTART_ONLY; then echo "仅重启"; elif $TEST_ONLY; then echo "仅测试"; else echo "重启+测试"; fi)"
    echo "批量任务数量: $BATCH_COUNT"
    if $TEST_ALL_TYPES; then
        echo "图像类型: 全部类型 (1=普通, 3=病理, 4=多通道)"
    else
        echo "图像类型: $IMAGE_TYPE"
    fi
    echo "============================================================"
    
    local success=true
    
    # 步骤1: 重启Celery (如果需要)
    if ! $TEST_ONLY; then
        if ! restart_celery; then
            log_error "Celery重启失败，终止执行"
            return 1
        fi
        
        if $RESTART_ONLY; then
            log_success "Celery重启完成"
            return 0
        fi
    fi
    
    # 步骤2: 运行端到端测试 (如果需要)
    if ! $RESTART_ONLY; then
        if ! run_e2e_test; then
            log_error "端到端测试失败"
            return 1
        fi
    fi
    
    log_success "所有操作完成成功"
    return 0
}

# 错误处理
trap 'log_error "脚本执行被中断"; exit 130' INT TERM

# 执行主函数
if main; then
    exit 0
else
    exit 1
fi
