#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
E2E测试主入口脚本

提供统一的测试入口，支持命令行和交互式运行
"""

import sys
import os
import argparse

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from tests.e2e.runners import E2ETestRunner
from tests.e2e.config import E2ETestConfig


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='AiLabel端到端测试工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s --env dev                    # 运行完整测试 (开发环境)
  %(prog)s --env prod --no-batch        # 跳过批量测试 (生产环境)
  %(prog)s --validate-only              # 仅运行系统验证
  %(prog)s --single-task --image-type 3 # 仅运行单任务测试
  %(prog)s --batch-only --batch-count 5 # 仅运行批量测试
  %(prog)s --test-all-types             # 测试所有图像类型
  %(prog)s --test-all-types --no-batch  # 测试所有图像类型(跳过批量测试)
        """
    )
    
    # 环境配置
    parser.add_argument('--env', choices=['dev', 'prod'], default='dev',
                       help='测试环境 (默认: dev)')
    
    # 测试类型选择
    test_group = parser.add_mutually_exclusive_group()
    test_group.add_argument('--validate-only', action='store_true',
                           help='仅运行系统验证测试')
    test_group.add_argument('--single-task', action='store_true',
                           help='仅运行单任务处理测试')
    test_group.add_argument('--batch-only', action='store_true',
                           help='仅运行批量任务测试')
    
    # 测试参数
    parser.add_argument('--no-batch', action='store_true',
                       help='跳过批量任务测试 (仅在完整测试时有效)')
    parser.add_argument('--image-type', type=int, choices=[1, 2, 3, 4], default=3,
                       help='图像类型ID (1=普通, 2=DICOM, 3=病理, 4=多通道, 默认: 3)')
    parser.add_argument('--test-all-types', action='store_true',
                       help='测试所有图像类型 (忽略--image-type参数)')
    parser.add_argument('--project-id', default='17',
                       help='项目ID (默认: 17)')
    parser.add_argument('--batch-count', type=int, default=0,
                       help='批量任务数量 (默认: 0=自动推荐数量)')
    parser.add_argument('--use-all-urls', action='store_true',
                       help='批量测试时循环使用所有可用URL')
    parser.add_argument('--max-batch-count', type=int, default=10,
                       help='最大批量任务数量限制 (默认: 10)')

    # 清理功能
    parser.add_argument('--cleanup', action='store_true',
                       help='测试完成后清理生成的测试文件')
    parser.add_argument('--cleanup-preview', action='store_true',
                       help='仅预览清理操作，不实际删除文件')
    parser.add_argument('--cleanup-max-age', type=int, default=1,
                       help='清理文件的最大年龄（小时，默认: 1）')
    
    # 输出控制
    parser.add_argument('--quiet', action='store_true',
                       help='减少输出信息')
    parser.add_argument('--verbose', action='store_true',
                       help='显示详细输出')
    
    args = parser.parse_args()
    
    # 创建配置
    config = E2ETestConfig(args.env)
    
    # 根据verbose/quiet调整日志级别
    if args.verbose:
        config.test_config['log_level'] = 'DEBUG'
    elif args.quiet:
        config.test_config['log_level'] = 'WARNING'
    
    # 创建测试运行器
    runner = E2ETestRunner(config)

    # 处理批量任务数量
    actual_batch_count = args.batch_count
    if actual_batch_count == 0:
        # 自动获取推荐数量
        from tests.e2e.core.senders.task_sender import get_recommended_batch_count
        actual_batch_count = get_recommended_batch_count(args.image_type, args.max_batch_count)
        if not args.quiet:
            print(f"📋 自动推荐批量任务数量: {actual_batch_count}")

    # 显示测试信息
    if not args.quiet:
        print("🎯 AiLabel端到端测试工具")
        print("=" * 50)
        print(f"测试环境: {args.env}")
        if args.test_all_types:
            print("图像类型: 全部类型 (1=普通, 3=病理, 4=多通道)")
        else:
            print(f"图像类型: {args.image_type}")
        print(f"项目ID: {args.project_id}")

        if args.validate_only:
            print("测试类型: 系统验证")
        elif args.single_task:
            print("测试类型: 单任务处理")
        elif args.batch_only:
            batch_info = f"{actual_batch_count}个任务"
            if args.use_all_urls:
                batch_info += " (使用所有URL)"
            print(f"测试类型: 批量任务处理 ({batch_info})")
        else:
            if args.no_batch:
                batch_info = "否"
            else:
                batch_info = f"是 ({actual_batch_count}个任务"
                if args.use_all_urls:
                    batch_info += ", 使用所有URL"
                batch_info += ")"
            print(f"测试类型: 完整测试 (批量测试: {batch_info})")

        print("=" * 50)
    
    # 运行测试
    try:
        if args.validate_only:
            # 仅系统验证
            result = runner.run_system_validation()
            success = result['status'] == 'healthy'
            
        elif args.single_task:
            # 仅单任务测试
            if args.test_all_types:
                # 测试所有图像类型的单任务
                from tests.e2e.core.senders.task_sender import ImageTaskSender
                sender = ImageTaskSender()
                available_types = list(sender.IMAGE_TYPE_CONFIGS.keys())

                all_results = {}
                overall_success = True

                for image_type_id in available_types:
                    type_name = sender._get_image_type_name(image_type_id)
                    print(f"\n🎯 测试图像类型 {image_type_id}: {type_name}")
                    print("=" * 50)

                    result = runner.run_task_processing_test(image_type_id, args.project_id)
                    all_results[f'type_{image_type_id}'] = result

                    if result['status'] != 'success':
                        overall_success = False
                        print(f"❌ {type_name} 测试失败")
                    else:
                        print(f"✅ {type_name} 测试成功")

                result = {
                    'status': 'success' if overall_success else 'partial_success',
                    'all_types_results': all_results,
                    'summary': f"测试了 {len(available_types)} 种图像类型"
                }
                success = overall_success
            else:
                result = runner.run_task_processing_test(args.image_type, args.project_id)
                success = result['status'] == 'success'
            
        elif args.batch_only:
            # 仅批量测试
            if args.test_all_types:
                # 测试所有图像类型的批量任务
                from tests.e2e.core.senders.task_sender import ImageTaskSender
                sender = ImageTaskSender()
                available_types = list(sender.IMAGE_TYPE_CONFIGS.keys())

                all_results = {}
                overall_success = True

                for image_type_id in available_types:
                    type_name = sender._get_image_type_name(image_type_id)
                    print(f"\n🎯 批量测试图像类型 {image_type_id}: {type_name}")
                    print("=" * 50)

                    result = runner.run_batch_processing_test(
                        actual_batch_count,
                        image_type_id,
                        use_all_urls=args.use_all_urls
                    )
                    all_results[f'type_{image_type_id}'] = result

                    if result['status'] not in ['success', 'partial_success']:
                        overall_success = False
                        print(f"❌ {type_name} 批量测试失败")
                    else:
                        print(f"✅ {type_name} 批量测试成功")

                result = {
                    'status': 'success' if overall_success else 'partial_success',
                    'all_types_results': all_results,
                    'summary': f"批量测试了 {len(available_types)} 种图像类型"
                }
                success = overall_success
            else:
                result = runner.run_batch_processing_test(
                    actual_batch_count,
                    args.image_type,
                    use_all_urls=args.use_all_urls
                )
                success = result['status'] in ['success', 'partial_success']

        else:
            # 完整测试
            result = runner.run_full_e2e_test(
                include_batch=not args.no_batch,
                batch_count=actual_batch_count,
                use_all_urls=args.use_all_urls,
                image_type_id=args.image_type,
                test_all_types=args.test_all_types
            )
            success = result['overall_status'] == 'success'
        
        # 执行清理操作（如果启用）
        if args.cleanup or args.cleanup_preview:
            try:
                from tests.e2e.core.cleaners import preview_nfs_cleanup, cleanup_nfs_test_files

                if not args.quiet:
                    print("\n" + "=" * 60)
                    print("🧹 测试文件清理")
                    print("=" * 60)

                if args.cleanup_preview:
                    # 仅预览清理操作
                    preview_result = preview_nfs_cleanup(args.project_id, args.cleanup_max_age)

                    if not args.quiet:
                        if preview_result['total_files_to_delete'] > 0:
                            print(f"📋 预览结果: 将删除 {preview_result['total_files_to_delete']} 个测试目录")
                            print(f"📋 将释放空间: {preview_result['total_size_formatted']}")
                            print("💡 使用 --cleanup 参数执行实际清理操作")
                        else:
                            print("📋 没有找到需要清理的测试文件")

                elif args.cleanup:
                    # 执行实际清理操作
                    if success:  # 只有测试成功时才自动清理
                        cleanup_result = cleanup_nfs_test_files(
                            args.project_id,
                            max_age_hours=args.cleanup_max_age,
                            confirm=True
                        )

                        if not args.quiet:
                            if cleanup_result['status'] == 'success':
                                print(f"✅ 清理完成: 删除 {cleanup_result['total_deleted']} 个目录")
                                print(f"📋 释放空间: {cleanup_result['total_size_freed_formatted']}")
                            elif cleanup_result['status'] == 'partial_success':
                                print(f"⚠️ 部分清理完成: 删除 {cleanup_result['total_deleted']} 个目录")
                                print(f"📋 释放空间: {cleanup_result['total_size_freed_formatted']}")
                                print(f"⚠️ {len(cleanup_result['failed_deletions'])} 个目录删除失败")
                            elif cleanup_result['status'] == 'no_files':
                                print("📋 没有找到需要清理的测试文件")
                            else:
                                print("❌ 清理操作失败")
                    else:
                        if not args.quiet:
                            print("⚠️ 测试失败，跳过自动清理操作")
                            print("💡 可以使用 --cleanup-preview 查看可清理的文件")

            except Exception as e:
                if not args.quiet:
                    print(f"❌ 清理操作异常: {e}")

        # 返回适当的退出码
        if success:
            if not args.quiet:
                print("\n✅ 测试成功完成!")
            sys.exit(0)
        else:
            if not args.quiet:
                print("\n❌ 测试失败，请查看详细日志")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⚠️ 测试被用户中断")
        sys.exit(130)
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        sys.exit(2)


if __name__ == "__main__":
    main()
