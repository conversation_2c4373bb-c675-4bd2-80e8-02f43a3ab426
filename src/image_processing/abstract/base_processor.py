"""
抽象图片处理基类

定义图片处理的抽象接口，包括读取、转换、保存等抽象方法和统一错误处理机制
"""

import os
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple, Union
from PIL import Image

from ..config.settings import ImageProcessingConfig


class ImageProcessingError(Exception):
    """图片处理异常基类"""
    pass


class UnsupportedFormatError(ImageProcessingError):
    """不支持的格式异常"""
    pass


class ProcessingTimeoutError(ImageProcessingError):
    """处理超时异常"""
    pass


class BaseImageProcessor(ABC):
    """
    抽象图片处理基类
    
    定义了图片处理的标准接口，所有具体的图片处理器都应该继承此类
    """
    
    def __init__(self, config: ImageProcessingConfig = None):
        """
        初始化处理器
        
        Args:
            config: 配置对象，如果为None则使用默认配置
        """
        self.config = config or ImageProcessingConfig()
        self.logger = logging.getLogger(self.__class__.__name__)
        
    @property
    @abstractmethod
    def supported_extensions(self) -> list:
        """返回支持的文件扩展名列表"""
        pass
    
    @property
    @abstractmethod
    def processor_type(self) -> str:
        """返回处理器类型标识"""
        pass
    
    def is_supported(self, file_path: str) -> bool:
        """
        检查文件是否被此处理器支持
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否支持
        """
        _, ext = os.path.splitext(file_path)
        return ext.lower() in self.supported_extensions
    
    def validate_input(self, input_path: str) -> None:
        """
        验证输入文件
        
        Args:
            input_path: 输入文件路径
            
        Raises:
            FileNotFoundError: 文件不存在
            UnsupportedFormatError: 不支持的格式
        """
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"输入文件不存在: {input_path}")
            
        if not self.is_supported(input_path):
            raise UnsupportedFormatError(
                f"不支持的文件格式: {input_path}, "
                f"支持的格式: {self.supported_extensions}"
            )
    
    def ensure_output_directory(self, output_path: str) -> None:
        """
        确保输出目录存在
        
        Args:
            output_path: 输出路径
        """
        output_dir = os.path.dirname(output_path)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            os.chmod(output_dir, self.config.DIR_PERMISSIONS)
    
    def set_file_permissions(self, file_path: str) -> None:
        """
        设置文件权限
        
        Args:
            file_path: 文件路径
        """
        try:
            os.chmod(file_path, self.config.FILE_PERMISSIONS)
        except Exception as e:
            self.logger.warning(f"设置文件权限失败 {file_path}: {e}")
    
    def create_thumbnail(self, image: Image.Image, output_path: str, 
                        image_name: str) -> None:
        """
        创建缩略图
        
        Args:
            image: PIL图像对象
            output_path: 输出目录路径
            image_name: 图像名称
        """
        try:
            thumbnail_dir = os.path.join(output_path, self.config.THUMBNAIL_SUBDIR)
            os.makedirs(thumbnail_dir, exist_ok=True)
            
            # 创建缩略图
            thumbnail = image.copy()
            thumbnail.thumbnail(self.config.THUMBNAIL_SIZE, Image.Resampling.LANCZOS)
            
            thumbnail_path = os.path.join(thumbnail_dir, f"{image_name}.png")
            thumbnail.save(thumbnail_path)
            self.set_file_permissions(thumbnail_path)
            
            self.logger.info(f"缩略图已创建: {thumbnail_path}")
            
        except Exception as e:
            self.logger.error(f"创建缩略图失败: {e}")
            raise ImageProcessingError(f"创建缩略图失败: {e}")
    
    @abstractmethod
    def process(self, input_path: str, output_path: str, 
                task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理图片的抽象方法
        
        Args:
            input_path: 输入文件路径
            output_path: 输出目录路径
            task_data: 任务数据，包含taskId, imageId, imageName等
            
        Returns:
            Dict[str, Any]: 处理结果
            
        Raises:
            ImageProcessingError: 处理失败
        """
        pass
    
    def handle_error(self, error: Exception, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        统一错误处理
        
        Args:
            error: 异常对象
            task_data: 任务数据
            
        Returns:
            Dict[str, Any]: 错误结果
        """
        error_msg = str(error)
        self.logger.error(f"处理失败: {error_msg}")
        
        return {
            'status': 'error',
            'error': error_msg,
            'task_id': task_data.get('taskId'),
            'image_id': task_data.get('imageId'),
            'processor_type': self.processor_type
        }
    
    def get_processing_result(self, task_data: Dict[str, Any], 
                            success: bool = True, 
                            additional_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        构建处理结果
        
        Args:
            task_data: 任务数据
            success: 是否成功
            additional_data: 额外数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        result = {
            'status': 'success' if success else 'error',
            'task_id': task_data.get('taskId'),
            'image_id': task_data.get('imageId'),
            'processor_type': self.processor_type,
            'timestamp': None  # 可以添加时间戳
        }
        
        if additional_data:
            result.update(additional_data)
            
        return result
