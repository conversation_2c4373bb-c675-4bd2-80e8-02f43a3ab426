"""
文件管理器

处理文件路径管理、输出目录处理、权限设置等
"""

import os
import shutil
import logging
from typing import Dict, Any, List, Optional
from ..config.settings import ImageProcessingConfig


class FileManager:
    """文件管理器"""
    
    def __init__(self, config: ImageProcessingConfig = None):
        """
        初始化文件管理器
        
        Args:
            config: 配置对象
        """
        self.config = config or ImageProcessingConfig()
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def validate_input_file(self, file_path: str) -> bool:
        """
        验证输入文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 文件是否有效
        """
        if not os.path.exists(file_path):
            self.logger.error(f"输入文件不存在: {file_path}")
            return False
        
        if not os.path.isfile(file_path):
            self.logger.error(f"路径不是文件: {file_path}")
            return False
        
        # 检查文件扩展名
        _, ext = os.path.splitext(file_path)
        if not self.config.is_supported_extension(ext):
            self.logger.error(f"不支持的文件格式: {ext}")
            return False
        
        return True
    
    def create_directory_structure(self, base_path: str, 
                                 subdirs: List[str] = None) -> Dict[str, str]:
        """
        创建目录结构
        
        Args:
            base_path: 基础路径
            subdirs: 子目录列表
            
        Returns:
            Dict[str, str]: 创建的目录路径映射
        """
        created_dirs = {'base': base_path}
        
        try:
            # 创建基础目录
            os.makedirs(base_path, exist_ok=True)
            os.chmod(base_path, self.config.DIR_PERMISSIONS)
            
            # 创建子目录
            if subdirs:
                for subdir in subdirs:
                    subdir_path = os.path.join(base_path, subdir)
                    os.makedirs(subdir_path, exist_ok=True)
                    os.chmod(subdir_path, self.config.DIR_PERMISSIONS)
                    created_dirs[subdir] = subdir_path
            
            self.logger.info(f"目录结构创建完成: {base_path}")
            return created_dirs
            
        except Exception as e:
            self.logger.error(f"创建目录结构失败: {e}")
            raise
    
    def get_project_output_path(self, project_id: int, image_name: str = None, 
                              subdir: str = None) -> str:
        """
        获取项目输出路径
        
        Args:
            project_id: 项目ID
            image_name: 图片名称
            subdir: 子目录
            
        Returns:
            str: 输出路径
        """
        return self.config.get_output_directory(project_id, image_name, subdir)
    
    def copy_file_with_permissions(self, src_path: str, dst_path: str) -> bool:
        """
        复制文件并设置权限
        
        Args:
            src_path: 源文件路径
            dst_path: 目标文件路径
            
        Returns:
            bool: 是否成功
        """
        try:
            # 确保目标目录存在
            dst_dir = os.path.dirname(dst_path)
            os.makedirs(dst_dir, exist_ok=True)
            
            # 复制文件
            shutil.copy2(src_path, dst_path)
            
            # 设置权限
            os.chmod(dst_path, self.config.FILE_PERMISSIONS)
            
            self.logger.info(f"文件复制完成: {src_path} -> {dst_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"文件复制失败: {e}")
            return False
    
    def set_recursive_permissions(self, path: str, 
                                file_permissions: int = None,
                                dir_permissions: int = None) -> None:
        """
        递归设置目录和文件权限
        
        Args:
            path: 路径
            file_permissions: 文件权限
            dir_permissions: 目录权限
        """
        if file_permissions is None:
            file_permissions = self.config.FILE_PERMISSIONS
        if dir_permissions is None:
            dir_permissions = self.config.DIR_PERMISSIONS
        
        try:
            for root, dirs, files in os.walk(path):
                # 设置目录权限
                os.chmod(root, dir_permissions)
                
                # 设置子目录权限
                for d in dirs:
                    dir_path = os.path.join(root, d)
                    os.chmod(dir_path, dir_permissions)
                
                # 设置文件权限
                for f in files:
                    file_path = os.path.join(root, f)
                    os.chmod(file_path, file_permissions)
            
            self.logger.info(f"权限设置完成: {path}")
            
        except Exception as e:
            self.logger.error(f"设置权限失败: {e}")
    
    def cleanup_temporary_files(self, temp_paths: List[str]) -> None:
        """
        清理临时文件
        
        Args:
            temp_paths: 临时文件路径列表
        """
        for temp_path in temp_paths:
            try:
                if os.path.exists(temp_path):
                    if os.path.isfile(temp_path):
                        os.remove(temp_path)
                    elif os.path.isdir(temp_path):
                        shutil.rmtree(temp_path)
                    self.logger.info(f"临时文件已清理: {temp_path}")
            except Exception as e:
                self.logger.warning(f"清理临时文件失败 {temp_path}: {e}")
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict[str, Any]: 文件信息
        """
        try:
            stat = os.stat(file_path)
            _, ext = os.path.splitext(file_path)
            
            return {
                'path': file_path,
                'name': os.path.basename(file_path),
                'extension': ext.lower(),
                'size': stat.st_size,
                'modified_time': stat.st_mtime,
                'is_supported': self.config.is_supported_extension(ext),
                'processor_type': self.config.get_processor_type_by_extension(ext)
            }
        except Exception as e:
            self.logger.error(f"获取文件信息失败: {e}")
            return {'error': str(e)}
    
    def create_standard_output_structure(self, project_id: int, 
                                       image_name: str,
                                       processor_type: str) -> Dict[str, str]:
        """
        创建标准输出目录结构
        
        Args:
            project_id: 项目ID
            image_name: 图片名称
            processor_type: 处理器类型
            
        Returns:
            Dict[str, str]: 创建的目录路径
        """
        base_path = self.get_project_output_path(project_id, image_name)
        
        # 根据处理器类型确定需要的子目录
        subdirs = []
        if processor_type == 'pathology':
            subdirs = [self.config.DEEPZOOM_SUBDIR, self.config.IMGS_SUBDIR]
        elif processor_type == 'multichannel':
            subdirs = [self.config.SPLIT_SUBDIR]
        else:
            subdirs = [self.config.THUMBNAIL_SUBDIR]
        
        return self.create_directory_structure(base_path, subdirs)
