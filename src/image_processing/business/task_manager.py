"""
任务管理器

处理图片转换任务的业务逻辑、状态管理、进度跟踪等
"""

import logging
from typing import Dict, Any, Optional
from ..config.settings import ImageProcessingConfig
from ..processors.common_image_processor import CommonImageProcessor
from ..processors.dicom_processor import DicomProcessor
from ..processors.pathology_processor import PathologyProcessor
from ..processors.multichannel_processor import MultichannelProcessor
from ..abstract.base_processor import BaseImageProcessor, ImageProcessingError


class TaskManager:
    """任务管理器"""
    
    def __init__(self, config: ImageProcessingConfig = None):
        """
        初始化任务管理器
        
        Args:
            config: 配置对象
        """
        self.config = config or ImageProcessingConfig()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化处理器映射
        self._processors = {
            'normal': CommonImageProcessor(self.config),
            'dicom': DicomProcessor(self.config),
            'pathology': PathologyProcessor(self.config),
            'multichannel': MultichannelProcessor(self.config)
        }
    
    def get_processor_by_type_id(self, image_type_id: int) -> Optional[BaseImageProcessor]:
        """
        根据图片类型ID获取处理器
        
        Args:
            image_type_id: 图片类型ID
            
        Returns:
            BaseImageProcessor: 对应的处理器，如果不支持则返回None
        """
        type_name = self.config.get_image_type_name(image_type_id)
        return self._processors.get(type_name)
    
    def get_processor_by_extension(self, file_path: str) -> Optional[BaseImageProcessor]:
        """
        根据文件扩展名获取处理器
        
        Args:
            file_path: 文件路径
            
        Returns:
            BaseImageProcessor: 对应的处理器，如果不支持则返回None
        """
        import os
        _, ext = os.path.splitext(file_path)
        processor_type = self.config.get_processor_type_by_extension(ext)
        return self._processors.get(processor_type)
    
    def validate_task_data(self, task_data: Dict[str, Any]) -> None:
        """
        验证任务数据
        
        Args:
            task_data: 任务数据
            
        Raises:
            ImageProcessingError: 验证失败
        """
        required_fields = ['taskId', 'imageId', 'imageName', 'imageUrl', 'projectId']
        missing_fields = [field for field in required_fields if not task_data.get(field)]
        
        if missing_fields:
            raise ImageProcessingError(f"缺少必要参数: {', '.join(missing_fields)}")
        
        # 验证图片类型ID（如果提供）
        image_type_id = task_data.get('imageTypeId')
        if image_type_id is not None:
            if image_type_id not in self.config.IMAGE_TYPE_MAPPING:
                raise ImageProcessingError(f"不支持的图片类型ID: {image_type_id}")
    
    def process_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理图片转换任务
        
        Args:
            task_data: 任务数据，包含以下字段：
                - taskId: 任务ID
                - imageId: 图片ID
                - imageName: 图片名称
                - imageUrl: 图片文件路径
                - projectId: 项目ID
                - imageTypeId: 图片类型ID (可选)
                
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 验证任务数据
            self.validate_task_data(task_data)
            
            task_id = task_data['taskId']
            image_url = task_data['imageUrl']
            project_id = task_data['projectId']
            image_name = task_data['imageName']
            
            self.logger.info(f"开始处理任务: {task_id}")
            
            # 选择处理器
            processor = None
            image_type_id = task_data.get('imageTypeId')
            
            if image_type_id is not None:
                # 根据类型ID选择处理器
                processor = self.get_processor_by_type_id(image_type_id)
                if not processor:
                    raise ImageProcessingError(f"不支持的图片类型ID: {image_type_id}")
            else:
                # 根据文件扩展名选择处理器
                processor = self.get_processor_by_extension(image_url)
                if not processor:
                    raise ImageProcessingError(f"无法确定文件类型: {image_url}")
            
            # 构建输出路径
            if processor.processor_type == 'pathology':
                # 病理图片需要特殊的目录结构
                output_path = self.config.get_output_directory(project_id, image_name)
            elif processor.processor_type == 'multichannel':
                # 多通道图片需要特殊的目录结构
                output_path = self.config.get_output_directory(project_id, image_name)
            else:
                # 普通图片和DICOM图片
                output_path = self.config.get_output_directory(project_id, None)
            
            self.logger.info(f"使用处理器: {processor.processor_type}")
            self.logger.info(f"输出路径: {output_path}")
            
            # 执行处理
            result = processor.process(image_url, output_path, task_data)
            
            self.logger.info(f"任务处理完成: {task_id}")
            return result
            
        except Exception as e:
            error_msg = f"任务处理失败: {e}"
            self.logger.error(error_msg)
            
            return {
                'status': 'error',
                'error': error_msg,
                'task_id': task_data.get('taskId'),
                'image_id': task_data.get('imageId')
            }
    
    def get_supported_formats(self) -> Dict[str, list]:
        """
        获取所有支持的格式
        
        Returns:
            Dict[str, list]: 处理器类型到支持格式的映射
        """
        return {
            processor_type: processor.supported_extensions
            for processor_type, processor in self._processors.items()
        }
    
    def get_processor_info(self) -> Dict[str, Dict[str, Any]]:
        """
        获取处理器信息
        
        Returns:
            Dict[str, Dict[str, Any]]: 处理器信息
        """
        return {
            processor_type: {
                'type': processor.processor_type,
                'supported_extensions': processor.supported_extensions,
                'class_name': processor.__class__.__name__
            }
            for processor_type, processor in self._processors.items()
        }
