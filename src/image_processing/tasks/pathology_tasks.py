"""
病理图像处理 Celery 任务

从原始 image_process.py 迁移的病理图像处理任务
"""

import os
import logging
from billiard import Pool
import openslide
import openslide.deepzoom

from src import celery, connect_redis
from src.decorators import time_logger
from ..config.settings import ImageProcessingConfig
from ..messaging.rabbitmq_handler import RabbitMQHandler

# 初始化组件
config = ImageProcessingConfig()
rabbitmq_handler = RabbitMQHandler(config)
redis_client = connect_redis()
logger = logging.getLogger(__name__)


def get_slide(wsi_path: str):
    """获取切片对象"""
    import os.path as osp
    from src.kfb.kfbreader import KFBSlide
    
    ext = osp.splitext(wsi_path)[1].lower()
    
    if ext in ['.svs', '.tif', '.tiff', '.mrxs']:
        return openslide.OpenSlide(wsi_path)
    elif ext in ['.kfb']:
        return KFBSlide(wsi_path)
    else:
        raise ValueError(f'不支持的扩展名: {wsi_path}')


def set_permissions(path: str):
    """递归设置目录权限"""
    for root, dirs, files in os.walk(path):
        if "deepzoom" not in root:
            continue
        os.chmod(root, config.DIR_PERMISSIONS)
        for d in dirs:
            os.chmod(os.path.join(root, d), config.DIR_PERMISSIONS)
        for f in files:
            os.chmod(os.path.join(root, f), config.FILE_PERMISSIONS)


def process_tile(level: int, x: int, y: int, mrxs_path: str, level_dir: str, 
                tileSize: int, overlap: int, taskId: str, totalNum: int, imageId: str):
    """处理单个切片"""
    try:
        slide = get_slide(mrxs_path)
        dz_gen = openslide.deepzoom.DeepZoomGenerator(
            slide, tile_size=tileSize, overlap=overlap, limit_bounds=False
        )
        tile = dz_gen.get_tile(level, (x, y))
        
        tile_path = os.path.join(level_dir, f'{x}_{y}.jpeg')
        tile.save(tile_path, 'JPEG')
        
        # 更新Redis进度
        if redis_client:
            processed_tiles = redis_client.incr(f'image_convert_task_progress:{taskId}')
            
            # 每处理指定数量的瓦片记录一次进度
            if processed_tiles % config.PROGRESS_UPDATE_INTERVAL == 0:
                progress = processed_tiles / totalNum
                rabbitmq_handler.send_progress_update(
                    task_id=taskId,
                    image_id=imageId,
                    status=1,  # 进行中
                    progress=progress,
                    result=f"{taskId}: 处理进度 {progress:.2%}"
                )
                logger.info(f"任务 {taskId} 进度: {progress:.2%} ({processed_tiles}/{totalNum})")
        
    except Exception as e:
        logger.error(f"处理瓦片 {x}, {y} 失败: {e}")


def generate_metadata(width: int, height: int, outputDir: str, tileSize: int):
    """生成元数据文件"""
    metadata = f'''<Image TileSize="{tileSize}" Overlap="0" Format="JPEG" xmlns="http://schemas.microsoft.com/deepzoom/2008">
  <Size Width="{str(width)}" Height="{str(height)}" /></Image>'''
    
    metadata_path = os.path.join(outputDir, "metadata.xml")
    with open(metadata_path, "w") as f:
        f.write(metadata)
    
    # 设置文件权限
    os.chmod(metadata_path, config.FILE_PERMISSIONS)
    logger.info(f"元数据文件已生成: {metadata_path}")


@celery.task
@time_logger
def generate_deep_zoom(mrxs_path: str, outputDir: str, tileSize: int, overlap: int, 
                      taskId: str, imageId: str):
    """
    Celery 任务：生成病理图像深度缩放图
    使用 billiard.Pool 进行多进程并行处理
    """
    try:
        logger.info(f"开始生成深度缩放图: {mrxs_path}")
        
        # 打开切片文件
        slide = get_slide(mrxs_path)
        os.makedirs(outputDir, exist_ok=True)
        
        # 创建深度缩放生成器
        dz_gen = openslide.deepzoom.DeepZoomGenerator(
            slide, tile_size=tileSize, overlap=overlap, limit_bounds=False
        )
        totalNum = sum(x * y for x, y in dz_gen.level_tiles)
        
        # 获取图像尺寸
        width, height = slide.dimensions
        
        logger.info(f"图像尺寸: {width} x {height}")
        logger.info(f"计算切片总数为: {totalNum}")
        
        num_processes = min(config.MAX_PROCESSES, totalNum)
        logger.info(f"当前可使用进程数为: {num_processes}")
        
        # 初始化 Redis 进度记录
        if redis_client:
            redis_client.set(f'image_convert_task_progress:{taskId}', '0')
        
        # 发送任务开始通知
        rabbitmq_handler.send_progress_update(
            task_id=taskId,
            image_id=imageId,
            status=1,  # 进行中
            progress=0.0,
            result=f"{taskId}: 开始生成深度图"
        )
        
        # 准备多进程任务
        tasks = []
        for level in range(dz_gen.level_count):
            level_dir = os.path.join(outputDir, config.IMGS_SUBDIR, str(level))
            os.makedirs(level_dir, exist_ok=True)
            tiles = dz_gen.level_tiles[level]
            
            for x in range(tiles[0]):
                for y in range(tiles[1]):
                    tasks.append((
                        level, x, y, mrxs_path, level_dir, 
                        tileSize, overlap, taskId, totalNum, imageId
                    ))
            
            set_permissions(level_dir)
        
        # 使用多进程处理瓦片
        with Pool(processes=num_processes) as pool:
            for args in tasks:
                pool.apply_async(process_tile, args=args)
            
            pool.close()
            pool.join()
        
        # 生成元数据
        generate_metadata(width, height, outputDir, tileSize)
        
        # 发送任务完成通知
        rabbitmq_handler.send_progress_update(
            task_id=taskId,
            image_id=imageId,
            status=2,  # 完成
            progress=1.0,
            result=f"{taskId}: 图像转化任务完成"
        )
        
        logger.info(f"深度缩放图生成完成: {outputDir}")
        
    except Exception as e:
        # 出现异常时发送错误回调
        error_msg = f"在生成深度图时发生异常: {e}"
        logger.error(error_msg)
        
        rabbitmq_handler.send_progress_update(
            task_id=taskId,
            image_id=imageId,
            status=3,  # 失败
            progress=0.0,
            result=f"图像转化任务失败: {str(e)}"
        )
        
        raise
