"""
图片处理分层架构模块

该模块提供了一个分层的图片处理架构，支持多种图片格式的处理：
- 普通图片 (PNG, JPG, JPEG)
- DICOM 医学影像 (DCM, DICOM)
- 病理切片 (SVS, TIF, TIFF, KFB)
- 多通道图像 (TIFF)

架构层次：
1. 抽象层 (abstract/): 定义图片处理的抽象接口
2. 处理器层 (processors/): 具体的图片格式处理实现
3. 业务逻辑层 (business/): 任务管理和文件管理
4. 消息队列层 (messaging/): RabbitMQ 连接和消息处理
5. 配置层 (config/): 集中化配置管理
"""

from .config.settings import ImageProcessingConfig
from .abstract.base_processor import BaseImageProcessor
from .business.task_manager import TaskManager
from .business.file_manager import FileManager
from .messaging.rabbitmq_handler import RabbitMQHandler

__version__ = "1.0.0"
__all__ = [
    "ImageProcessingConfig",
    "BaseImageProcessor", 
    "TaskManager",
    "FileManager",
    "RabbitMQHandler"
]
