"""
RabbitMQ 处理器

管理 RabbitMQ 连接、消息处理、连接池等
"""

import json
import logging
import contextlib
from typing import Dict, Any, Optional, Callable
import pika
import pika.exceptions

from config.config import (
    RABBITMQ_HOST, RABBITMQ_PORT, RABBITMQ_ACCOUNT, RABBITMQ_PASSWORD,
    RABBITMQ_HEARTBEAT, RABBITMQ_BLOCKED_CONNECTION_TIMEOUT,
    RABBITMQ_CONNECTION_ATTEMPTS, RABBITMQ_RETRY_DELAY, RABBITMQ_SOCKET_TIMEOUT
)
from ..config.settings import ImageProcessingConfig
from .message_serializer import MessageSerializer


class RabbitMQConnectionError(Exception):
    """RabbitMQ连接异常"""
    pass


class RabbitMQHandler:
    """RabbitMQ处理器"""
    
    def __init__(self, config: ImageProcessingConfig = None):
        """
        初始化RabbitMQ处理器
        
        Args:
            config: 配置对象
        """
        self.config = config or ImageProcessingConfig()
        self.logger = logging.getLogger(self.__class__.__name__)
        self.serializer = MessageSerializer()
        
        # 连接参数
        self.connection_params = pika.ConnectionParameters(
            host=RABBITMQ_HOST,
            port=RABBITMQ_PORT,
            virtual_host='/',
            credentials=pika.PlainCredentials(RABBITMQ_ACCOUNT, RABBITMQ_PASSWORD),
            heartbeat=RABBITMQ_HEARTBEAT,
            blocked_connection_timeout=RABBITMQ_BLOCKED_CONNECTION_TIMEOUT,
            connection_attempts=RABBITMQ_CONNECTION_ATTEMPTS,
            retry_delay=RABBITMQ_RETRY_DELAY,
            socket_timeout=RABBITMQ_SOCKET_TIMEOUT,
        )
    
    @contextlib.contextmanager
    def get_connection(self):
        """
        RabbitMQ连接上下文管理器，确保连接正确关闭
        
        Yields:
            pika.channel.Channel: RabbitMQ通道
            
        Raises:
            RabbitMQConnectionError: 连接失败
        """
        connection = None
        channel = None
        
        try:
            connection = pika.BlockingConnection(self.connection_params)
            channel = connection.channel()
            
            self.logger.debug("RabbitMQ连接已建立")
            yield channel
            
        except pika.exceptions.AMQPConnectionError as e:
            error_msg = f"RabbitMQ连接失败: {e}"
            self.logger.error(error_msg)
            raise RabbitMQConnectionError(error_msg)
        except Exception as e:
            error_msg = f"RabbitMQ操作异常: {e}"
            self.logger.error(error_msg)
            raise RabbitMQConnectionError(error_msg)
        finally:
            # 清理资源
            try:
                if channel and not channel.is_closed:
                    channel.close()
                    self.logger.debug("RabbitMQ通道已关闭")
            except Exception as e:
                self.logger.warning(f"关闭通道时出错: {e}")
            
            try:
                if connection and not connection.is_closed:
                    connection.close()
                    self.logger.debug("RabbitMQ连接已关闭")
            except Exception as e:
                self.logger.warning(f"关闭连接时出错: {e}")
    
    def declare_queue(self, channel, queue_name: str, durable: bool = True,
                     with_dlx: bool = True) -> None:
        """
        声明队列
        
        Args:
            channel: RabbitMQ通道
            queue_name: 队列名称
            durable: 是否持久化
            with_dlx: 是否配置死信队列
        """
        try:
            arguments = {}
            if with_dlx:
                arguments.update({
                    'x-dead-letter-exchange': self.config.DEAD_LETTER_EXCHANGE,
                    'x-dead-letter-routing-key': self.config.DEAD_LETTER_ROUTING_KEY
                })
            
            channel.queue_declare(
                queue=queue_name,
                durable=durable,
                arguments=arguments if arguments else None
            )
            
            self.logger.debug(f"队列已声明: {queue_name}")
            
        except Exception as e:
            self.logger.error(f"声明队列失败 {queue_name}: {e}")
            raise
    
    def send_message(self, queue_name: str, message_data: Dict[str, Any],
                    persistent: bool = True) -> bool:
        """
        发送消息到队列
        
        Args:
            queue_name: 队列名称
            message_data: 消息数据
            persistent: 是否持久化消息
            
        Returns:
            bool: 是否发送成功
        """
        try:
            with self.get_connection() as channel:
                # 声明队列（确保队列存在）
                self.declare_queue(channel, queue_name)
                
                # 序列化消息
                message_body = self.serializer.serialize(message_data)
                
                # 设置消息属性
                properties = pika.BasicProperties(
                    delivery_mode=2 if persistent else 1,  # 2 = 持久化
                    content_type='application/json'
                )
                
                # 发送消息
                channel.basic_publish(
                    exchange='',
                    routing_key=queue_name,
                    body=message_body,
                    properties=properties
                )
                
                self.logger.info(f"消息已发送到队列 {queue_name}: {message_data.get('taskId', 'unknown')}")
                return True
                
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            return False
    
    def send_progress_update(self, task_id: str, image_id: str, 
                           status: int, progress: float, 
                           result: str) -> bool:
        """
        发送进度更新消息
        
        Args:
            task_id: 任务ID
            image_id: 图片ID
            status: 状态 (1=进行中, 2=完成, 3=失败)
            progress: 进度 (0.0-1.0)
            result: 结果描述
            
        Returns:
            bool: 是否发送成功
        """
        message_data = {
            "taskId": task_id,
            "imageId": image_id,
            "status": status,
            "progress": progress,
            "result": result
        }
        
        return self.send_message(
            self.config.IMAGE_TASK_FINISH_CALLBACK_QUEUE,
            message_data
        )
    
    def consume_messages(self, queue_name: str, 
                        callback: Callable[[Any, Any, Any, bytes], None],
                        auto_ack: bool = False) -> None:
        """
        消费队列消息
        
        Args:
            queue_name: 队列名称
            callback: 消息处理回调函数
            auto_ack: 是否自动确认消息
        """
        try:
            with self.get_connection() as channel:
                # 声明队列
                self.declare_queue(channel, queue_name)
                
                # 设置消费者
                channel.basic_consume(
                    queue=queue_name,
                    on_message_callback=callback,
                    auto_ack=auto_ack
                )
                
                self.logger.info(f"开始消费队列: {queue_name}")
                channel.start_consuming()
                
        except KeyboardInterrupt:
            self.logger.info("消费者被用户中断")
        except Exception as e:
            self.logger.error(f"消费消息失败: {e}")
            raise
    
    def get_queue_info(self, queue_name: str) -> Optional[Dict[str, Any]]:
        """
        获取队列信息
        
        Args:
            queue_name: 队列名称
            
        Returns:
            Dict[str, Any]: 队列信息，失败时返回None
        """
        try:
            with self.get_connection() as channel:
                method = channel.queue_declare(queue=queue_name, passive=True)
                
                return {
                    'queue': queue_name,
                    'message_count': method.method.message_count,
                    'consumer_count': method.method.consumer_count
                }
                
        except Exception as e:
            self.logger.error(f"获取队列信息失败 {queue_name}: {e}")
            return None
    
    def test_connection(self) -> bool:
        """
        测试连接
        
        Returns:
            bool: 连接是否正常
        """
        try:
            with self.get_connection() as channel:
                self.logger.info("RabbitMQ连接测试成功")
                return True
        except Exception as e:
            self.logger.error(f"RabbitMQ连接测试失败: {e}")
            return False
