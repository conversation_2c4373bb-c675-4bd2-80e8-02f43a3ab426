"""
消息序列化器

处理消息的序列化和反序列化
"""

import json
import logging
from typing import Dict, Any, Union, Optional
from datetime import datetime


class MessageSerializationError(Exception):
    """消息序列化异常"""
    pass


class MessageSerializer:
    """消息序列化器"""
    
    def __init__(self):
        """初始化序列化器"""
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def serialize(self, data: Dict[str, Any]) -> str:
        """
        序列化消息数据
        
        Args:
            data: 要序列化的数据
            
        Returns:
            str: 序列化后的JSON字符串
            
        Raises:
            MessageSerializationError: 序列化失败
        """
        try:
            # 添加时间戳
            if 'timestamp' not in data:
                data['timestamp'] = datetime.now().isoformat()
            
            # 序列化为JSON
            json_str = json.dumps(data, ensure_ascii=False, separators=(',', ':'))
            
            self.logger.debug(f"消息序列化成功: {len(json_str)} 字符")
            return json_str
            
        except (TypeError, ValueError) as e:
            error_msg = f"消息序列化失败: {e}"
            self.logger.error(error_msg)
            raise MessageSerializationError(error_msg)
    
    def deserialize(self, json_str: Union[str, bytes]) -> Dict[str, Any]:
        """
        反序列化消息数据
        
        Args:
            json_str: JSON字符串或字节
            
        Returns:
            Dict[str, Any]: 反序列化后的数据
            
        Raises:
            MessageSerializationError: 反序列化失败
        """
        try:
            # 处理字节类型
            if isinstance(json_str, bytes):
                json_str = json_str.decode('utf-8')
            
            # 反序列化JSON
            data = json.loads(json_str)
            
            if not isinstance(data, dict):
                raise MessageSerializationError("消息数据必须是字典类型")
            
            self.logger.debug(f"消息反序列化成功: {len(data)} 个字段")
            return data
            
        except (json.JSONDecodeError, UnicodeDecodeError) as e:
            error_msg = f"消息反序列化失败: {e}"
            self.logger.error(error_msg)
            raise MessageSerializationError(error_msg)
    
    def create_task_message(self, task_id: str, image_id: str, 
                          image_name: str, image_url: str, 
                          project_id: int, image_type_id: int = None) -> Dict[str, Any]:
        """
        创建任务消息
        
        Args:
            task_id: 任务ID
            image_id: 图片ID
            image_name: 图片名称
            image_url: 图片URL
            project_id: 项目ID
            image_type_id: 图片类型ID
            
        Returns:
            Dict[str, Any]: 任务消息数据
        """
        message = {
            'taskId': task_id,
            'imageId': image_id,
            'imageName': image_name,
            'imageUrl': image_url,
            'projectId': project_id
        }
        
        if image_type_id is not None:
            message['imageTypeId'] = image_type_id
        
        return message
    
    def create_progress_message(self, task_id: str, image_id: str,
                              status: int, progress: float,
                              result: str) -> Dict[str, Any]:
        """
        创建进度消息
        
        Args:
            task_id: 任务ID
            image_id: 图片ID
            status: 状态 (1=进行中, 2=完成, 3=失败)
            progress: 进度 (0.0-1.0)
            result: 结果描述
            
        Returns:
            Dict[str, Any]: 进度消息数据
        """
        return {
            'taskId': task_id,
            'imageId': image_id,
            'status': status,
            'progress': progress,
            'result': result
        }
    
    def create_error_message(self, task_id: str, image_id: str,
                           error: str) -> Dict[str, Any]:
        """
        创建错误消息
        
        Args:
            task_id: 任务ID
            image_id: 图片ID
            error: 错误信息
            
        Returns:
            Dict[str, Any]: 错误消息数据
        """
        return self.create_progress_message(
            task_id=task_id,
            image_id=image_id,
            status=3,  # 失败状态
            progress=0.0,
            result=f"处理失败: {error}"
        )
    
    def validate_task_message(self, message: Dict[str, Any]) -> bool:
        """
        验证任务消息格式
        
        Args:
            message: 消息数据
            
        Returns:
            bool: 是否有效
        """
        required_fields = ['taskId', 'imageId', 'imageName', 'imageUrl', 'projectId']
        
        for field in required_fields:
            if field not in message or not message[field]:
                self.logger.error(f"任务消息缺少必要字段: {field}")
                return False
        
        # 验证数据类型
        if not isinstance(message['projectId'], int):
            self.logger.error("projectId 必须是整数类型")
            return False
        
        if 'imageTypeId' in message and not isinstance(message['imageTypeId'], int):
            self.logger.error("imageTypeId 必须是整数类型")
            return False
        
        return True
    
    def validate_progress_message(self, message: Dict[str, Any]) -> bool:
        """
        验证进度消息格式
        
        Args:
            message: 消息数据
            
        Returns:
            bool: 是否有效
        """
        required_fields = ['taskId', 'imageId', 'status', 'progress', 'result']
        
        for field in required_fields:
            if field not in message:
                self.logger.error(f"进度消息缺少必要字段: {field}")
                return False
        
        # 验证数据类型和范围
        if not isinstance(message['status'], int) or message['status'] not in [1, 2, 3]:
            self.logger.error("status 必须是 1, 2, 3 中的一个")
            return False
        
        if not isinstance(message['progress'], (int, float)) or not (0.0 <= message['progress'] <= 1.0):
            self.logger.error("progress 必须是 0.0-1.0 之间的数值")
            return False
        
        return True
