"""
图片处理配置管理

集中管理所有硬编码配置项，包括队列名称、文件路径、处理参数等
"""

import os
from typing import Dict, List, Tuple
from config.config import PROJECT_SAVE_DIR


class ImageProcessingConfig:
    """图片处理配置类"""
    
    # ================================= 队列配置 =================================
    
    # 队列名称
    IMAGE_CONVERT_QUEUE = 'medlabel_image_convert_queue'
    IMAGE_TASK_FINISH_CALLBACK_QUEUE = 'medlabel_image_convert_task_finish_callback_queue'
    
    # 死信队列配置
    DEAD_LETTER_EXCHANGE = 'dlx.direct'
    DEAD_LETTER_ROUTING_KEY = 'image_convert_task_finish_callback.dlq'
    
    # ================================= 图片处理参数 =================================
    
    # 瓦片处理参数
    DEFAULT_TILE_SIZE = 1024
    DEFAULT_OVERLAP = 0
    THUMBNAIL_SIZE = (224, 224)
    
    # 进程配置
    MAX_PROCESSES = max(1, 64)
    DEFAULT_WORKER_PROCESSES = 10
    
    # 进度更新配置
    PROGRESS_UPDATE_INTERVAL = 500  # 每处理500个瓦片发送一次进度更新
    
    # ================================= 文件格式配置 =================================
    
    # 图片类型ID映射
    IMAGE_TYPE_MAPPING = {
        1: 'normal',      # 普通图片 (PNG, JPG, JPEG)
        2: 'dicom',       # DICOM医学影像 (DCM, DICOM)
        3: 'pathology',   # 病理切片 (SVS, TIF, TIFF, KFB)
        4: 'multichannel' # 多通道图像 (TIFF)
    }
    
    # 支持的文件扩展名
    NORMAL_IMAGE_EXTENSIONS = ['.png', '.jpg', '.jpeg']
    DICOM_EXTENSIONS = ['.dcm', '.dicom']
    PATHOLOGY_EXTENSIONS = ['.svs', '.tif', '.tiff', '.mrxs', '.kfb']
    MULTICHANNEL_EXTENSIONS = ['.tiff']
    
    # 所有支持的扩展名
    ALL_SUPPORTED_EXTENSIONS = (
        NORMAL_IMAGE_EXTENSIONS + 
        DICOM_EXTENSIONS + 
        PATHOLOGY_EXTENSIONS + 
        MULTICHANNEL_EXTENSIONS
    )
    
    # ================================= DICOM处理配置 =================================
    
    # DICOM窗宽窗位配置
    DICOM_WINDOW_WIDTH = 1800
    DICOM_WINDOW_LEVEL = 1000
    
    # ================================= 文件路径配置 =================================
    
    # 项目保存目录
    PROJECT_SAVE_DIR = PROJECT_SAVE_DIR
    
    # 输出目录结构
    DEEPZOOM_SUBDIR = "deepzoom"
    THUMBNAIL_SUBDIR = "thumbnail"
    SPLIT_SUBDIR = "split"
    IMGS_SUBDIR = "imgs"
    
    # 元数据文件名
    METADATA_FILENAME = "metadata.xml"
    
    # ================================= 权限配置 =================================
    
    # 文件权限
    FILE_PERMISSIONS = 0o777
    DIR_PERMISSIONS = 0o777
    
    # ================================= 超时和重试配置 =================================
    
    # 任务超时配置（秒）
    TASK_SOFT_TIMEOUT = 1800  # 30分钟
    TASK_HARD_TIMEOUT = 2400  # 40分钟
    
    # 连接重试配置
    MAX_RETRY_ATTEMPTS = 3
    RETRY_DELAY = 1
    
    # ================================= 辅助方法 =================================
    
    @classmethod
    def get_image_type_name(cls, type_id: int) -> str:
        """根据类型ID获取图片类型名称"""
        return cls.IMAGE_TYPE_MAPPING.get(type_id, 'unknown')
    
    @classmethod
    def is_supported_extension(cls, extension: str) -> bool:
        """检查文件扩展名是否支持"""
        return extension.lower() in cls.ALL_SUPPORTED_EXTENSIONS
    
    @classmethod
    def get_processor_type_by_extension(cls, extension: str) -> str:
        """根据文件扩展名获取处理器类型"""
        ext_lower = extension.lower()
        
        if ext_lower in cls.NORMAL_IMAGE_EXTENSIONS:
            return 'normal'
        elif ext_lower in cls.DICOM_EXTENSIONS:
            return 'dicom'
        elif ext_lower in cls.PATHOLOGY_EXTENSIONS:
            return 'pathology'
        elif ext_lower in cls.MULTICHANNEL_EXTENSIONS:
            return 'multichannel'
        else:
            return 'unknown'
    
    @classmethod
    def get_output_directory(cls, project_id: int, image_name: str, 
                           subdir: str = None, channel: int = None) -> str:
        """构建输出目录路径"""
        base_path = os.path.join(cls.PROJECT_SAVE_DIR, str(project_id))
        
        if image_name:
            base_path = os.path.join(base_path, image_name)
        
        if subdir:
            base_path = os.path.join(base_path, subdir)
            
        if channel is not None:
            base_path = os.path.join(base_path, str(channel))
            
        return base_path
    
    @classmethod
    def get_metadata_template(cls, width: int, height: int, tile_size: int = None) -> str:
        """获取元数据XML模板"""
        if tile_size is None:
            tile_size = cls.DEFAULT_TILE_SIZE
            
        return f'''<Image TileSize="{tile_size}" Overlap="{cls.DEFAULT_OVERLAP}" Format="JPEG" xmlns="http://schemas.microsoft.com/deepzoom/2008">
  <Size Width="{width}" Height="{height}" /></Image>'''
