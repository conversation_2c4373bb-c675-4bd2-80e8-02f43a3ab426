"""
病理图片处理器

处理 SVS、TIF、TIFF、KFB 等病理切片格式，生成深度缩放图
"""

import os
import os.path as osp
from typing import Dict, Any
from PIL import Image
import openslide
from openslide import OpenSlide
import openslide.deepzoom
from billiard import Pool

from ..abstract.base_processor import BaseImageProcessor, ImageProcessingError
from ..config.settings import ImageProcessingConfig
from src.kfb.kfbreader import KFBSlide
from src import connect_redis


class PathologyProcessor(BaseImageProcessor):
    """病理图片处理器"""
    
    def __init__(self, config: ImageProcessingConfig = None):
        super().__init__(config)
        self.redis_client = connect_redis()
    
    @property
    def supported_extensions(self) -> list:
        """返回支持的文件扩展名列表"""
        return self.config.PATHOLOGY_EXTENSIONS
    
    @property
    def processor_type(self) -> str:
        """返回处理器类型标识"""
        return 'pathology'
    
    def get_slide(self, wsi_path: str):
        """
        获取切片对象
        
        Args:
            wsi_path: 切片文件路径
            
        Returns:
            切片对象 (OpenSlide 或 KFBSlide)
        """
        ext = osp.splitext(wsi_path)[1].lower()
        
        if ext in ['.svs', '.tif', '.tiff', '.mrxs']:
            return OpenSlide(wsi_path)
        elif ext in ['.kfb']:
            return KFBSlide(wsi_path)
        else:
            raise ImageProcessingError(f'不支持的扩展名: {wsi_path}')
    
    def read_region(self, slide, location, level, size, zero_level_loc=True) -> Image:
        """
        读取切片指定层级的指定区域
        
        Args:
            slide: 切片对象
            location: 要读取区域的左上角坐标(x, y)
            level: 要读取的缩放层级
            size: 要读取的区域图片大小
            zero_level_loc: 若为True，则location参数为左上角在level 0上的坐标
            
        Returns:
            PIL.Image: 区域图像
        """
        ratio = slide.level_downsamples[level] / slide.level_downsamples[0]
        
        if isinstance(slide, KFBSlide):
            if zero_level_loc:
                return Image.fromarray(slide.read_region(
                    (round(location[0]/ratio), round(location[1]/ratio)), level, size
                ))
            return Image.fromarray(slide.read_region(location, level, size))
        elif isinstance(slide, OpenSlide):
            if zero_level_loc:
                return slide.read_region(location, level, size)
            return slide.read_region(
                (round(location[0]*ratio), round(location[1]*ratio)), level, size
            )
        else:
            raise ImageProcessingError(f'不支持的切片类型: {type(slide)}')
    
    def set_permissions_recursive(self, path: str):
        """
        递归设置目录权限
        
        Args:
            path: 目录路径
        """
        for root, dirs, files in os.walk(path):
            if "deepzoom" not in root:
                continue
            os.chmod(root, self.config.DIR_PERMISSIONS)
            for d in dirs:
                os.chmod(os.path.join(root, d), self.config.DIR_PERMISSIONS)
            for f in files:
                os.chmod(os.path.join(root, f), self.config.FILE_PERMISSIONS)
    
    def process_tile(self, level: int, x: int, y: int, slide_path: str, 
                    level_dir: str, tile_size: int, overlap: int, 
                    task_id: str, total_num: int, image_id: str):
        """
        处理单个切片并更新 Redis 进度
        
        Args:
            level: 层级
            x, y: 瓦片坐标
            slide_path: 切片文件路径
            level_dir: 层级输出目录
            tile_size: 瓦片大小
            overlap: 重叠像素
            task_id: 任务ID
            total_num: 总瓦片数
            image_id: 图片ID
        """
        try:
            slide = self.get_slide(slide_path)
            dz_gen = openslide.deepzoom.DeepZoomGenerator(
                slide, tile_size=tile_size, overlap=overlap, limit_bounds=False
            )
            tile = dz_gen.get_tile(level, (x, y))
            
            tile_path = os.path.join(level_dir, f'{x}_{y}.jpeg')
            tile.save(tile_path, 'JPEG')
            
            # 更新Redis进度
            if self.redis_client:
                processed_tiles = self.redis_client.incr(f'image_convert_task_processed:{task_id}')
                
                # 每处理指定数量的瓦片记录一次进度
                if processed_tiles % self.config.PROGRESS_UPDATE_INTERVAL == 0:
                    progress = round(processed_tiles / total_num, 2)
                    self.logger.info(f"任务 {task_id} 进度: {progress:.2%} ({processed_tiles}/{total_num})")
            
        except Exception as e:
            self.logger.error(f"处理瓦片 {x}, {y} 失败: {e}")
    
    def generate_metadata(self, width: int, height: int, output_dir: str, 
                         tile_size: int = None) -> str:
        """
        生成元数据文件
        
        Args:
            width: 图像宽度
            height: 图像高度
            output_dir: 输出目录
            tile_size: 瓦片大小
            
        Returns:
            str: 元数据文件路径
        """
        if tile_size is None:
            tile_size = self.config.DEFAULT_TILE_SIZE
            
        metadata_content = self.config.get_metadata_template(width, height, tile_size)
        metadata_path = os.path.join(output_dir, self.config.METADATA_FILENAME)
        
        with open(metadata_path, 'w') as f:
            f.write(metadata_content)
        
        self.set_file_permissions(metadata_path)
        return metadata_path

    def process(self, input_path: str, output_path: str,
                task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理病理图片，生成深度缩放图

        Args:
            input_path: 输入文件路径
            output_path: 输出目录路径
            task_data: 任务数据

        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 验证输入
            self.validate_input(input_path)

            # 获取任务参数
            task_id = task_data.get('taskId')
            image_id = task_data.get('imageId')
            image_name = task_data.get('imageName')

            if not all([task_id, image_id, image_name]):
                raise ImageProcessingError("缺少必要参数: taskId, imageId, imageName")

            # 构建深度缩放输出目录
            deepzoom_output = os.path.join(output_path, self.config.DEEPZOOM_SUBDIR)
            self.ensure_output_directory(deepzoom_output)

            # 获取处理参数
            tile_size = self.config.DEFAULT_TILE_SIZE
            overlap = self.config.DEFAULT_OVERLAP

            # 打开切片文件
            try:
                slide = self.get_slide(input_path)
            except openslide.OpenSlideError as e:
                raise ImageProcessingError(f"无法打开切片文件 {input_path}: {e}")

            # 创建深度缩放生成器
            dz_gen = openslide.deepzoom.DeepZoomGenerator(
                slide, tile_size=tile_size, overlap=overlap, limit_bounds=False
            )

            # 计算总瓦片数
            total_num = sum(x * y for x, y in dz_gen.level_tiles)
            width, height = slide.dimensions

            self.logger.info(f"开始处理病理图片: {input_path}")
            self.logger.info(f"图像尺寸: {width} x {height}")
            self.logger.info(f"计算切片总数: {total_num}")

            # 初始化Redis进度记录
            if self.redis_client:
                self.redis_client.set(f'image_convert_task_processed:{task_id}', '0')

            # 生成元数据文件
            metadata_path = self.generate_metadata(width, height, deepzoom_output, tile_size)

            # 准备多进程任务
            tasks = []
            for level in range(dz_gen.level_count):
                level_dir = os.path.join(deepzoom_output, self.config.IMGS_SUBDIR, str(level))
                os.makedirs(level_dir, exist_ok=True)
                tiles = dz_gen.level_tiles[level]

                for x in range(tiles[0]):
                    for y in range(tiles[1]):
                        tasks.append((
                            level, x, y, input_path, level_dir,
                            tile_size, overlap, task_id, total_num, image_id
                        ))

                self.set_permissions_recursive(level_dir)

            # 使用多进程处理瓦片
            num_processes = min(self.config.MAX_PROCESSES, len(tasks))
            self.logger.info(f"使用进程数: {num_processes}")

            with Pool(processes=num_processes) as pool:
                for args in tasks:
                    pool.apply_async(self.process_tile, args=args)

                pool.close()
                pool.join()

            self.logger.info(f"病理图片处理完成: {input_path}")

            return self.get_processing_result(
                task_data,
                success=True,
                additional_data={
                    'output_dir': deepzoom_output,
                    'metadata_file': metadata_path,
                    'total_tiles': total_num,
                    'image_dimensions': (width, height),
                    'message': f"{task_id}: 图像转化任务完成"
                }
            )

        except Exception as e:
            return self.handle_error(e, task_data)
