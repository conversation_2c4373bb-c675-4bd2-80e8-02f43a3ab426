"""
多通道图片处理器

处理 TIFF 多通道图像，进行通道分离和深度缩放处理
"""

import os
import math
from typing import Dict, Any, List, Tuple
import numpy as np
from PIL import Image
import tifffile as tiff
from billiard import Pool

from ..abstract.base_processor import BaseImageProcessor, ImageProcessingError
from ..config.settings import ImageProcessingConfig


class MultichannelProcessor(BaseImageProcessor):
    """多通道图片处理器"""
    
    @property
    def supported_extensions(self) -> list:
        """返回支持的文件扩展名列表"""
        return self.config.MULTICHANNEL_EXTENSIONS
    
    @property
    def processor_type(self) -> str:
        """返回处理器类型标识"""
        return 'multichannel'
    
    def init_worker(self, shared_img):
        """多进程工作器初始化函数"""
        global global_img
        global_img = shared_img
    
    def process_channel(self, channel_index: int, output_dir: str) -> str:
        """
        处理单个通道
        
        Args:
            channel_index: 通道索引
            output_dir: 输出目录
            
        Returns:
            str: 处理结果信息
        """
        global global_img
        
        # 从全局变量中取出通道
        channel = global_img[channel_index]
        output_path = os.path.join(output_dir, f'channel_{channel_index + 1}.tiff')
        tiff.imwrite(output_path, channel)
        
        return f"[PID {os.getpid()}] 写入通道 {channel_index + 1}"
    
    def split_channels(self, input_path: str, output_dir: str) -> List[str]:
        """
        分离多通道图像
        
        Args:
            input_path: 输入文件路径
            output_dir: 输出目录
            
        Returns:
            List[str]: 分离后的通道文件路径列表
        """
        try:
            # 读取多通道图像
            img = tiff.imread(input_path)
            self.logger.info("读取多通道图像完成")
            
            if img.ndim < 3:
                raise ImageProcessingError(f"图像不是多通道格式: {img.shape}")
            
            channels = img.shape[0]
            self.logger.info(f"检测到 {channels} 个通道")
            
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 准备多进程任务
            tasks = [(i, output_dir) for i in range(channels)]
            
            # 使用多进程处理通道分离
            with Pool(processes=channels, initializer=self.init_worker, initargs=(img,)) as pool:
                results = []
                for args in tasks:
                    r = pool.apply_async(self.process_channel, args=args)
                    results.append(r)
                
                pool.close()
                pool.join()
                
                # 获取处理结果
                for r in results:
                    self.logger.info(r.get())
            
            # 返回分离后的文件路径列表
            channel_paths = []
            for i in range(channels):
                channel_path = os.path.join(output_dir, f'channel_{i + 1}.tiff')
                channel_paths.append(channel_path)
            
            return channel_paths
            
        except Exception as e:
            raise ImageProcessingError(f"通道分离失败: {e}")
    
    def count_total_tiles(self, max_level: int, width: int, height: int, 
                         tile_size: int) -> Dict[int, Tuple[int, int]]:
        """
        计算每个层级的瓦片数量
        
        Args:
            max_level: 最大层级
            width: 图像宽度
            height: 图像高度
            tile_size: 瓦片大小
            
        Returns:
            Dict[int, Tuple[int, int]]: 每个层级的瓦片数量 {level: (tiles_x, tiles_y)}
        """
        level_tile_counts = {}
        total_tiles = 0
        
        for level in range(max_level + 1):
            # 计算当前层的缩放比例
            scale_factor = 2 ** (max_level - level)
            scaled_height = math.ceil(height / scale_factor)
            scaled_width = math.ceil(width / scale_factor)
            
            # 计算该层的瓦片数量
            num_tiles_x = math.ceil(scaled_width / tile_size)
            num_tiles_y = math.ceil(scaled_height / tile_size)
            
            level_tile_counts[level] = (num_tiles_x, num_tiles_y)
            total_tiles += num_tiles_x * num_tiles_y
        
        self.logger.info(f"总层数: {max_level + 1}")
        self.logger.info(f"总瓦片数: {total_tiles}")
        
        return level_tile_counts
    
    def generate_deep_zoom_for_tiff(self, tiff_path: str, output_dir: str, 
                                   tile_size: int = None) -> str:
        """
        为TIFF文件生成深度缩放图
        
        Args:
            tiff_path: TIFF文件路径
            output_dir: 输出目录
            tile_size: 瓦片大小
            
        Returns:
            str: 元数据文件路径
        """
        if tile_size is None:
            tile_size = self.config.DEFAULT_TILE_SIZE
        
        try:
            # 读取图像
            image = tiff.imread(tiff_path)
            
            if image.ndim != 2:
                raise ImageProcessingError(f"图像不是灰度图（2D），而是 {image.shape}")
            
            # 数据类型转换
            if image.dtype != np.uint8:
                image = ((image - image.min()) / (image.max() - image.min()) * 255).astype(np.uint8)
            
            height, width = image.shape[:2]
            max_level = int(math.ceil(math.log(max(height, width), 2)))
            
            level_tile_counts = self.count_total_tiles(max_level, width, height, tile_size)
            
            # 处理每个层级的切片
            for level in range(max_level + 1):
                scale_factor = 2 ** (max_level - level)
                scaled_width = math.ceil(width / scale_factor)
                scaled_height = math.ceil(height / scale_factor)
                
                # 使用PIL对整个图像进行缩放
                pil_image = Image.fromarray(image)
                level_image = pil_image.resize((scaled_width, scaled_height), Image.Resampling.LANCZOS)
                
                level_dir = os.path.join(output_dir, self.config.IMGS_SUBDIR, str(level))
                os.makedirs(level_dir, exist_ok=True)
                
                num_tiles_x, num_tiles_y = level_tile_counts[level]
                
                for x in range(num_tiles_x):
                    for y in range(num_tiles_y):
                        left = x * tile_size
                        top = y * tile_size
                        right = min(left + tile_size, scaled_width)
                        bottom = min(top + tile_size, scaled_height)
                        
                        if right <= left or bottom <= top:
                            continue
                        
                        # 从缩放后的图像裁剪出瓦片
                        tile = level_image.crop((left, top, right, bottom))
                        tile_filename = os.path.join(level_dir, f"{x}_{y}.jpeg")
                        tile.save(tile_filename, "JPEG")
                
                # 设置权限
                self.set_permissions_recursive(level_dir)
            
            # 生成元数据
            metadata_path = self.generate_metadata(width, height, output_dir, tile_size)
            return metadata_path
            
        except Exception as e:
            raise ImageProcessingError(f"TIFF深度缩放生成失败: {e}")
    
    def set_permissions_recursive(self, path: str):
        """递归设置目录权限"""
        for root, dirs, files in os.walk(path):
            os.chmod(root, self.config.DIR_PERMISSIONS)
            for d in dirs:
                os.chmod(os.path.join(root, d), self.config.DIR_PERMISSIONS)
            for f in files:
                os.chmod(os.path.join(root, f), self.config.FILE_PERMISSIONS)
    
    def generate_metadata(self, width: int, height: int, output_dir: str, 
                         tile_size: int) -> str:
        """生成元数据文件"""
        metadata_content = self.config.get_metadata_template(width, height, tile_size)
        metadata_path = os.path.join(output_dir, self.config.METADATA_FILENAME)
        
        with open(metadata_path, 'w') as f:
            f.write(metadata_content)
        
        self.set_file_permissions(metadata_path)
        return metadata_path

    def process(self, input_path: str, output_path: str,
                task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理多通道图片

        Args:
            input_path: 输入文件路径
            output_path: 输出目录路径
            task_data: 任务数据

        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 验证输入
            self.validate_input(input_path)

            # 获取任务参数
            task_id = task_data.get('taskId')
            image_id = task_data.get('imageId')
            image_name = task_data.get('imageName')
            project_id = task_data.get('projectId')

            if not all([task_id, image_id, image_name, project_id]):
                raise ImageProcessingError("缺少必要参数: taskId, imageId, imageName, projectId")

            # 构建分离输出目录
            split_output = os.path.join(output_path, self.config.SPLIT_SUBDIR)
            self.ensure_output_directory(split_output)

            self.logger.info(f"开始处理多通道图片: {input_path}")

            # 第一步：分离通道
            channel_paths = self.split_channels(input_path, split_output)
            self.logger.info("通道分离完成")

            # 第二步：为每个通道生成深度缩放图
            channel_results = []
            for i, channel_path in enumerate(channel_paths):
                channel_output_dir = self.config.get_output_directory(
                    project_id, image_name, channel=i+1
                )
                os.makedirs(channel_output_dir, exist_ok=True)

                try:
                    metadata_path = self.generate_deep_zoom_for_tiff(
                        channel_path, channel_output_dir
                    )

                    channel_results.append({
                        'channel': i + 1,
                        'channel_file': channel_path,
                        'output_dir': channel_output_dir,
                        'metadata_file': metadata_path
                    })

                    self.logger.info(f"通道 {i+1} 深度缩放完成")

                except Exception as e:
                    self.logger.error(f"通道 {i+1} 处理失败: {e}")
                    channel_results.append({
                        'channel': i + 1,
                        'error': str(e)
                    })

            self.logger.info(f"多通道图片处理完成: {input_path}")

            return self.get_processing_result(
                task_data,
                success=True,
                additional_data={
                    'split_output': split_output,
                    'channel_paths': channel_paths,
                    'channel_results': channel_results,
                    'total_channels': len(channel_paths),
                    'message': f"{task_id}: 多通道图像转化任务完成"
                }
            )

        except Exception as e:
            return self.handle_error(e, task_data)
