"""
DICOM图片处理器

处理 DCM、DICOM 格式的医学影像
"""

import os
import numpy as np
from typing import Dict, Any
from PIL import Image
import SimpleIT<PERSON> as sitk

from ..abstract.base_processor import BaseImageProcessor, ImageProcessingError
from ..config.settings import ImageProcessingConfig


class DicomProcessor(BaseImageProcessor):
    """DICOM图片处理器"""
    
    @property
    def supported_extensions(self) -> list:
        """返回支持的文件扩展名列表"""
        return self.config.DICOM_EXTENSIONS
    
    @property
    def processor_type(self) -> str:
        """返回处理器类型标识"""
        return 'dicom'
    
    def normalize_pixel_value(self, pixel_value: float) -> int:
        """
        DICOM像素值归一化
        
        Args:
            pixel_value: 原始像素值
            
        Returns:
            int: 归一化后的像素值 (0-255)
        """
        window_width = self.config.DICOM_WINDOW_WIDTH
        window_level = self.config.DICOM_WINDOW_LEVEL
        
        low = window_level - window_width / 2
        high = window_level + window_width / 2
        
        if pixel_value < low:
            return 0
        elif pixel_value > high:
            return 255
        else:
            return int((pixel_value - low) * (255 / window_width))
    
    def convert_dicom_to_png(self, input_path: str, output_path: str, 
                           image_name: str) -> str:
        """
        将DICOM文件转换为PNG格式
        
        Args:
            input_path: DICOM文件路径
            output_path: 输出目录路径
            image_name: 图片名称
            
        Returns:
            str: 输出文件路径
        """
        try:
            # 读取DICOM图像
            ds_array = sitk.ReadImage(input_path)
            pixel_array = sitk.GetArrayFromImage(ds_array)
            
            # 归一化处理
            normalized_array = []
            for z in pixel_array:
                y_array = []
                for y in z:
                    x_array = []
                    for x in y:
                        normalized_value = self.normalize_pixel_value(x)
                        x_array.append(normalized_value)
                    y_array.append(x_array)
                normalized_array.append(y_array)
            
            # 转换为uint8格式
            normalized_array = np.array(normalized_array).astype('uint8')
            
            # 保存为PNG
            img = sitk.GetImageFromArray(normalized_array)
            output_file_path = os.path.join(output_path, f"{image_name}.png")
            sitk.WriteImage(img, output_file_path)
            self.set_file_permissions(output_file_path)
            
            self.logger.info(f"DICOM转换完成: {input_path} -> {output_file_path}")
            return output_file_path
            
        except Exception as e:
            raise ImageProcessingError(f"DICOM转换失败: {e}")
    
    def process(self, input_path: str, output_path: str, 
                task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理DICOM图片
        
        Args:
            input_path: 输入文件路径
            output_path: 输出目录路径
            task_data: 任务数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 验证输入
            self.validate_input(input_path)
            
            # 获取任务参数
            image_name = task_data.get('imageName')
            if not image_name:
                raise ImageProcessingError("缺少图片名称参数")
            
            # 确保输出目录存在
            self.ensure_output_directory(output_path)
            
            # 转换DICOM为PNG
            output_file_path = self.convert_dicom_to_png(
                input_path, output_path, image_name
            )
            
            # 创建缩略图
            with Image.open(output_file_path) as img:
                self.create_thumbnail(img, output_path, image_name)
            
            return self.get_processing_result(
                task_data,
                success=True,
                additional_data={
                    'output_file': output_file_path,
                    'message': f"{task_data.get('taskId')}: DICOM图像转化任务完成"
                }
            )
            
        except Exception as e:
            return self.handle_error(e, task_data)
