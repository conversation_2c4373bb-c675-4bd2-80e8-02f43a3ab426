"""
普通图片处理器

处理 PNG、JPG、JPEG 格式的普通图片
"""

import os
import shutil
from typing import Dict, Any
from PIL import Image

from ..abstract.base_processor import BaseImageProcessor, ImageProcessingError
from ..config.settings import ImageProcessingConfig


class CommonImageProcessor(BaseImageProcessor):
    """普通图片处理器"""
    
    @property
    def supported_extensions(self) -> list:
        """返回支持的文件扩展名列表"""
        return self.config.NORMAL_IMAGE_EXTENSIONS
    
    @property
    def processor_type(self) -> str:
        """返回处理器类型标识"""
        return 'normal'
    
    def process(self, input_path: str, output_path: str, 
                task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理普通图片
        
        Args:
            input_path: 输入文件路径
            output_path: 输出目录路径
            task_data: 任务数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 验证输入
            self.validate_input(input_path)
            
            # 获取任务参数
            image_name = task_data.get('imageName')
            if not image_name:
                raise ImageProcessingError("缺少图片名称参数")
            
            # 确保输出目录存在
            self.ensure_output_directory(output_path)
            
            # 复制原图到输出目录
            output_file_path = os.path.join(output_path, f"{image_name}.png")
            shutil.copy(input_path, output_file_path)
            self.set_file_permissions(output_file_path)
            
            self.logger.info(f"普通图片已复制: {input_path} -> {output_file_path}")
            
            # 创建缩略图
            with Image.open(output_file_path) as img:
                self.create_thumbnail(img, output_path, image_name)
            
            return self.get_processing_result(
                task_data, 
                success=True,
                additional_data={
                    'output_file': output_file_path,
                    'message': f"{task_data.get('taskId')}: 图像转化任务完成"
                }
            )
            
        except Exception as e:
            return self.handle_error(e, task_data)
