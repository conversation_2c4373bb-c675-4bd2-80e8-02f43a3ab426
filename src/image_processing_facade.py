"""
图片处理门面 (Facade)

提供与原始 image_process.py 兼容的接口，内部使用新的分层架构实现。
这个门面允许现有代码无缝迁移到新架构，同时保持向后兼容性。

使用方式：
1. 直接替换导入：from src.image_process import xxx -> from src.image_processing_facade import xxx
2. 或者在原始文件中导入此门面的函数
"""

import json
import logging
import os
from typing import Dict, Any, Optional

# Celery 相关导入
from . import celery
from src import connect_redis

# 新的分层架构导入
from .image_processing.config.settings import ImageProcessingConfig
from .image_processing.business.task_manager import TaskManager
from .image_processing.messaging.rabbitmq_handler import RabbitMQHandler
from .image_processing.messaging.message_serializer import MessageSerializer

# 初始化组件
config = ImageProcessingConfig()
task_manager = TaskManager(config)
rabbitmq_handler = RabbitMQHandler(config)
message_serializer = MessageSerializer()
redis_client = connect_redis()

# 配置日志
logger = logging.getLogger(__name__)

# ================================= 兼容性常量 =================================

# 保持向后兼容的全局变量
image_task_finish_callback_queue = config.IMAGE_TASK_FINISH_CALLBACK_QUEUE
global_img = None

# ================================= 兼容性函数 =================================

def get_rabbitmq_connection():
    """
    兼容性函数：RabbitMQ连接上下文管理器
    现在使用新的 RabbitMQHandler
    """
    return rabbitmq_handler.get_connection()

def send_progress_update(taskId: str, imageId: str, status: int, 
                        progress: float, result: str) -> None:
    """
    兼容性函数：发送进度更新消息
    现在使用新的 RabbitMQHandler
    
    Args:
        taskId: 任务ID
        imageId: 图片ID
        status: 状态 (1=进行中, 2=完成, 3=失败)
        progress: 进度 (0.0-1.0)
        result: 结果描述
    """
    try:
        success = rabbitmq_handler.send_progress_update(
            task_id=taskId,
            image_id=imageId,
            status=status,
            progress=progress,
            result=result
        )
        
        if success:
            logger.info(f"进度更新已发送: {taskId} - {progress:.2%}")
        else:
            logger.warning(f"进度更新发送失败: {taskId}")
            
    except Exception as e:
        logger.error(f"发送进度更新失败: {e}")
        # 不抛出异常，避免影响主任务

# ================================= 业务处理函数 =================================

def normal_image_process(data: Dict[str, Any], reply_channel=None) -> None:
    """
    兼容性函数：普通图片处理
    现在使用新的 TaskManager
    
    Args:
        data: 任务数据
        reply_channel: 兼容性参数（已废弃）
    """
    try:
        logger.info(f"开始处理普通图片任务: {data.get('taskId')}")
        
        # 设置图片类型ID为普通图片
        data['imageTypeId'] = 1
        
        # 使用新的任务管理器处理
        result = task_manager.process_task(data)
        
        # 发送进度更新
        if result['status'] == 'success':
            send_progress_update(
                taskId=data['taskId'],
                imageId=data['imageId'],
                status=2,  # 完成
                progress=1.0,
                result=result.get('message', f"{data['taskId']}: 图像转化任务完成")
            )
            logger.info(f"普通图片处理完成: {data['taskId']}")
        else:
            send_progress_update(
                taskId=data['taskId'],
                imageId=data['imageId'],
                status=3,  # 失败
                progress=0.0,
                result=result.get('error', '处理失败')
            )
            logger.error(f"普通图片处理失败: {data['taskId']}")
            
    except Exception as e:
        logger.error(f"普通图片处理异常: {e}")
        send_progress_update(
            taskId=data.get('taskId', 'unknown'),
            imageId=data.get('imageId', 'unknown'),
            status=3,
            progress=0.0,
            result=f"处理异常: {str(e)}"
        )

def dicom_image_process(data: Dict[str, Any], reply_channel=None) -> None:
    """
    兼容性函数：DICOM图片处理
    现在使用新的 TaskManager
    
    Args:
        data: 任务数据
        reply_channel: 兼容性参数（已废弃）
    """
    try:
        logger.info(f"开始处理DICOM图片任务: {data.get('taskId')}")
        
        # 设置图片类型ID为DICOM
        data['imageTypeId'] = 2
        
        # 使用新的任务管理器处理
        result = task_manager.process_task(data)
        
        # 发送进度更新
        if result['status'] == 'success':
            send_progress_update(
                taskId=data['taskId'],
                imageId=data['imageId'],
                status=2,  # 完成
                progress=1.0,
                result=result.get('message', f"{data['taskId']}: DICOM图像转化任务完成")
            )
            logger.info(f"DICOM图片处理完成: {data['taskId']}")
        else:
            send_progress_update(
                taskId=data['taskId'],
                imageId=data['imageId'],
                status=3,  # 失败
                progress=0.0,
                result=result.get('error', '处理失败')
            )
            logger.error(f"DICOM图片处理失败: {data['taskId']}")
            
    except Exception as e:
        logger.error(f"DICOM图片处理异常: {e}")
        send_progress_update(
            taskId=data.get('taskId', 'unknown'),
            imageId=data.get('imageId', 'unknown'),
            status=3,
            progress=0.0,
            result=f"处理异常: {str(e)}"
        )

def patho_image_process(data: Dict[str, Any], reply_channel=None) -> None:
    """
    兼容性函数：病理图片处理
    现在使用新的 TaskManager，支持异步处理

    Args:
        data: 任务数据
        reply_channel: 兼容性参数（已废弃）
    """
    try:
        logger.info(f"开始处理病理图片任务: {data.get('taskId')}")

        # 设置图片类型ID为病理图片
        data['imageTypeId'] = 3

        # 发送任务开始通知
        send_progress_update(
            taskId=data['taskId'],
            imageId=data['imageId'],
            status=1,  # 进行中
            progress=0.0,
            result=f"{data['taskId']}: 开始生成深度图"
        )

        # 使用Celery异步处理病理图片（保持原有的异步特性）
        from .image_process import generate_deep_zoom

        # 构建输出路径
        project_id = data['projectId']
        image_name = data['imageName']
        output_dir = config.get_output_directory(project_id, image_name, config.DEEPZOOM_SUBDIR)

        # 提交Celery任务
        task = generate_deep_zoom.apply_async(args=[
            data['imageUrl'],  # mrxs_path
            output_dir,        # outputDir
            config.DEFAULT_TILE_SIZE,  # tileSize
            config.DEFAULT_OVERLAP,    # overlap
            data['taskId'],    # taskId
            data['imageId']    # imageId
        ])

        logger.info(f"病理图片Celery任务已提交: {task.id}")

    except Exception as e:
        logger.error(f"病理图片处理异常: {e}")
        send_progress_update(
            taskId=data.get('taskId', 'unknown'),
            imageId=data.get('imageId', 'unknown'),
            status=3,
            progress=0.0,
            result=f"处理异常: {str(e)}"
        )

def channel_image_process(data: Dict[str, Any], reply_channel=None) -> None:
    """
    兼容性函数：多通道图片处理
    现在使用新的 TaskManager，支持异步处理

    Args:
        data: 任务数据
        reply_channel: 兼容性参数（已废弃）
    """
    try:
        logger.info(f"开始处理多通道图片任务: {data.get('taskId')}")

        # 设置图片类型ID为多通道图片
        data['imageTypeId'] = 4

        # 发送任务开始通知
        send_progress_update(
            taskId=data['taskId'],
            imageId=data['imageId'],
            status=1,  # 进行中
            progress=0.0,
            result=f"{data['taskId']}: 开始处理多通道图像"
        )

        # 使用新的任务管理器处理
        result = task_manager.process_task(data)

        # 发送进度更新
        if result['status'] == 'success':
            send_progress_update(
                taskId=data['taskId'],
                imageId=data['imageId'],
                status=2,  # 完成
                progress=1.0,
                result=result.get('message', f"{data['taskId']}: 多通道图像转化任务完成")
            )
            logger.info(f"多通道图片处理完成: {data['taskId']}")
        else:
            send_progress_update(
                taskId=data['taskId'],
                imageId=data['imageId'],
                status=3,  # 失败
                progress=0.0,
                result=result.get('error', '处理失败')
            )
            logger.error(f"多通道图片处理失败: {data['taskId']}")

    except Exception as e:
        logger.error(f"多通道图片处理异常: {e}")
        send_progress_update(
            taskId=data.get('taskId', 'unknown'),
            imageId=data.get('imageId', 'unknown'),
            status=3,
            progress=0.0,
            result=f"处理异常: {str(e)}"
        )
