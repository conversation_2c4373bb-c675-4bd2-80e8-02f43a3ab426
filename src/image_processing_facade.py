"""
图片处理门面 (Facade)

简化的门面接口，提供统一的图片处理入口。
内部使用新的分层架构实现，支持多种图片格式的处理。
"""

import logging
from typing import Dict, Any

# 新的分层架构导入
from .image_processing.config.settings import ImageProcessingConfig
from .image_processing.business.task_manager import TaskManager
from .image_processing.messaging.rabbitmq_handler import RabbitMQHandler

# 初始化组件
config = ImageProcessingConfig()
task_manager = TaskManager(config)
rabbitmq_handler = RabbitMQHandler(config)

# 配置日志
logger = logging.getLogger(__name__)

# ================================= 核心处理函数 =================================

def process_image_task(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    统一的图片处理入口

    Args:
        data: 任务数据，包含：
            - taskId: 任务ID
            - imageId: 图片ID
            - imageName: 图片名称
            - imageUrl: 图片文件路径
            - projectId: 项目ID
            - imageTypeId: 图片类型ID (可选，会自动检测)

    Returns:
        Dict[str, Any]: 处理结果
    """
    try:
        logger.info(f"开始处理图片任务: {data.get('taskId')}")

        # 使用新的任务管理器处理
        result = task_manager.process_task(data)

        # 发送进度更新
        if result['status'] == 'success':
            _send_progress_update(
                task_id=data['taskId'],
                image_id=data['imageId'],
                status=2,  # 完成
                progress=1.0,
                result=result.get('message', f"{data['taskId']}: 图像转化任务完成")
            )
            logger.info(f"图片处理完成: {data['taskId']}")
        else:
            _send_progress_update(
                task_id=data['taskId'],
                image_id=data['imageId'],
                status=3,  # 失败
                progress=0.0,
                result=result.get('error', '处理失败')
            )
            logger.error(f"图片处理失败: {data['taskId']}")

        return result

    except Exception as e:
        logger.error(f"图片处理异常: {e}")
        error_result = {
            'status': 'error',
            'error': str(e),
            'task_id': data.get('taskId'),
            'image_id': data.get('imageId')
        }

        _send_progress_update(
            task_id=data.get('taskId', 'unknown'),
            image_id=data.get('imageId', 'unknown'),
            status=3,
            progress=0.0,
            result=f"处理异常: {str(e)}"
        )

        return error_result

def _send_progress_update(task_id: str, image_id: str, status: int,
                         progress: float, result: str) -> None:
    """
    内部函数：发送进度更新消息
    """
    try:
        success = rabbitmq_handler.send_progress_update(
            task_id=task_id,
            image_id=image_id,
            status=status,
            progress=progress,
            result=result
        )

        if not success:
            logger.warning(f"进度更新发送失败: {task_id}")

    except Exception as e:
        logger.error(f"发送进度更新失败: {e}")

# ================================= 兼容性函数 =================================

def normal_image_process(data: Dict[str, Any], reply_channel=None) -> None:
    """兼容性函数：普通图片处理"""
    data['imageTypeId'] = 1
    process_image_task(data)

def dicom_image_process(data: Dict[str, Any], reply_channel=None) -> None:
    """兼容性函数：DICOM图片处理"""
    data['imageTypeId'] = 2
    process_image_task(data)

def patho_image_process(data: Dict[str, Any], reply_channel=None) -> None:
    """兼容性函数：病理图片处理"""
    data['imageTypeId'] = 3
    process_image_task(data)

def channel_image_process(data: Dict[str, Any], reply_channel=None) -> None:
    """兼容性函数：多通道图片处理"""
    data['imageTypeId'] = 4
    process_image_task(data)

def patho_image_process(data: Dict[str, Any], reply_channel=None) -> None:
    """兼容性函数：病理图片处理"""
    data['imageTypeId'] = 3
    process_image_task(data)

def channel_image_process(data: Dict[str, Any], reply_channel=None) -> None:
    """兼容性函数：多通道图片处理"""
    data['imageTypeId'] = 4
    process_image_task(data)

def image_process(channel, method, properties, body):
    """
    主要的图片处理入口函数，兼容原始接口

    Args:
        channel: RabbitMQ通道
        method: 消息方法
        properties: 消息属性
        body: 消息体
    """
    try:
        logger.info("接收到消息，开始执行图像转化任务")

        # 解析消息
        data = rabbitmq_handler.serializer.deserialize(body)
        logger.info(f"任务数据: {data}")

        # 获取图片类型ID
        image_type_id = data.get('imageTypeId')

        if image_type_id == 1:
            normal_image_process(data)
        elif image_type_id == 2:
            dicom_image_process(data)
        elif image_type_id == 3:
            patho_image_process(data)
        elif image_type_id == 4:
            channel_image_process(data)
        else:
            # 尝试自动检测图片类型
            result = process_image_task(data)
            if result['status'] == 'error':
                logger.error(f"无法识别图片类型: {image_type_id}")

        # 确认消息
        if hasattr(channel, 'basic_ack'):
            channel.basic_ack(delivery_tag=method.delivery_tag)

    except Exception as e:
        logger.error(f"image_process主函数异常: {e}")

        # 发送错误消息
        try:
            if 'data' in locals():
                _send_progress_update(
                    task_id=data.get('taskId', 'unknown'),
                    image_id=data.get('imageId', 'unknown'),
                    status=3,
                    progress=0.0,
                    result=f"主函数异常: {str(e)}"
                )
        except Exception as publish_error:
            logger.error(f"发送错误消息失败: {publish_error}")

        # 拒绝消息
        if hasattr(channel, 'basic_nack'):
            channel.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
