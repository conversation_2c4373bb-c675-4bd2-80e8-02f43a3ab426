import pika
import time
import logging
import os
import redis
import threading
from celery import Celery
from config.config import (RABBITMQ_HOST, RABBITMQ_PORT,
                    RABBITMQ_ACCOUNT, RABBITMQ_PASSWORD,
                    REDIS_HOST, REDIS_PORT)

def create_celery() -> Celery:
    """创建Celery实例，不依赖Flask应用"""
    core = os.cpu_count()
    print("当前可用CPU数量为:", core)

    # 直接创建Celery实例
    celery = Celery('ailabel_worker')

    # 配置Celery
    celery.conf.update({
        # 连接配置
        'broker_url': f'pyamqp://admin:vipa%40404@{RABBITMQ_HOST}:{RABBITMQ_PORT}//',
        'result_backend': f'redis://{REDIS_HOST}:{REDIS_PORT}/0',
        'broker_connection_timeout': 60,        # 增加连接超时时间
        'broker_heartbeat': 300,                # 减少心跳间隔，更快检测连接问题
        'broker_pool_limit': 10,                # 限制连接池大小，避免连接泄漏
        'broker_connection_retry': True,        # 启用连接重试
        'broker_connection_retry_on_startup': True,
        'broker_connection_max_retries': 10,    # 最大重试次数

        # 任务确认配置
        'task_acks_late': True,                 # 任务完成后才确认
        'task_reject_on_worker_lost': True,     # worker丢失时拒绝任务
        'task_acks_on_failure_or_timeout': True, # 失败或超时时确认任务

        # Worker配置
        'worker_prefetch_multiplier': 1,        # 每次只预取一个任务
        'worker_max_tasks_per_child': 50,       # 减少每个子进程处理的任务数
        'worker_disable_rate_limits': True,     # 禁用速率限制
        'worker_concurrency': 10,               # Worker并发数

        # 超时配置
        'task_soft_time_limit': 1800,           # 柔性超时30分钟
        'task_time_limit': 2400,                # 硬性超时40分钟
        'result_expires': 7200,                 # 结果保存2小时

        # 序列化配置
        'task_serializer': 'json',
        'result_serializer': 'json',
        'accept_content': ['json'],

        # 其他配置
        'worker_send_task_events': True,        # 发送任务事件
        'task_send_sent_event': True,           # 发送任务发送事件

        # 队列配置 - 标注平台专用队列，只处理图像处理任务
        'task_routes': {
            '*': {'queue': 'ailabel_queue'}  # 只监听标注平台队列
        },
        'task_default_queue': 'ailabel_queue',
        'task_default_exchange': 'ailabel_exchange',
        'task_default_routing_key': 'ailabel_routing_key'
    })
    return celery

# 创建Celery实例
celery = create_celery()

# Redis连接池配置
conn_pool = redis.ConnectionPool(host=f'{REDIS_HOST}',port=REDIS_PORT)

current_path = os.path.dirname(os.path.abspath(__file__))
def connect_redis():
    try:
        connect = redis.Redis(connection_pool=conn_pool)
        print('Redis连接成功')
        return connect
    except Exception:
        print('Redis连接失败')
        return None

logger = logging.getLogger('app')
logger.setLevel(logging.DEBUG)


# 防止循环导入
from .image_process import image_process
# 连接到RabbitMQ服务器
user_info = pika.PlainCredentials(RABBITMQ_ACCOUNT, RABBITMQ_PASSWORD)

# 声明队列
medlabel_image_convert_queue = 'medlabel_image_convert_queue'

# 消费图像转化队列
def consume_convert():
    while True:
        try:
            connection = pika.BlockingConnection(pika.ConnectionParameters(RABBITMQ_HOST, RABBITMQ_PORT, '/', user_info, heartbeat=0))
            channel = connection.channel()
            channel.queue_declare(queue=medlabel_image_convert_queue,
                                  durable=True,
                                  arguments={
                                      'x-dead-letter-exchange': 'dlx.direct',  # 必须加上这一行
                                      'x-dead-letter-routing-key': 'image_convert.dlq'
                                  })
            # 设置 prefetch_count 为 1，确保一次只接收一条消息
            channel.basic_qos(prefetch_count=1)

            # 包装 image_process 函数以添加手动确认
            def wrapped_image_process(channel, method, properties, body):
                try:
                    print("🎯 开始处理图像转换任务")
                    image_process(channel, method, properties, body)
                    # 任务成功完成，手动确认消息
                    channel.basic_ack(delivery_tag=method.delivery_tag)
                    print("✅ 图像转换任务完成，消息已确认")
                except Exception as e:
                    print(f"❌ 图像转换任务异常: {e}")
                    import traceback
                    print(f"📋 详细错误信息: {traceback.format_exc()}")
                    # 发生异常时也要确认消息，避免无限重试
                    channel.basic_ack(delivery_tag=method.delivery_tag)
                    print("⚠️ 异常情况下已确认消息，避免重复处理")

            channel.basic_consume(medlabel_image_convert_queue, wrapped_image_process, auto_ack=False)
            channel.start_consuming()
        except Exception as e:
            print(f"Exception in thread : {e}")
            time.sleep(5)

# 创建并启动图像转化线程
thread1 = threading.Thread(target=consume_convert)
thread1.start()

thread2 = threading.Thread(target=consume_convert)
thread2.start()

thread3 = threading.Thread(target=consume_convert)
thread3.start()

thread4 = threading.Thread(target=consume_convert)
thread4.start()

thread5 = threading.Thread(target=consume_convert)
thread5.start()

# 移除Flask Web服务相关代码
# 保留Celery Worker和RabbitMQ消息消费功能