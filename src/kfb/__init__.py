"""
KFB (Kfb Format Binary) 图像处理模块

该模块提供对 KFB 格式病理图像的读取和处理功能。
KFB 是一种专用的病理图像格式，常用于数字病理学应用。

主要功能：
- KFB 格式图像的读取和解析
- 多层级图像数据访问
- 区域图像提取
- 缩略图生成
- 关联图像访问

主要类和函数：
- KFBSlide: KFB 图像的主要接口类
- KFBSlideError: KFB 相关异常类
"""

from .kfbreader import (
    KFBSlide,
    KFBSlideError,
    kfbslide_open,
    kfbslide_close,
    kfbslide_get_level_count,
    kfbslide_get_level_dimensions,
    kfbslide_get_level_downsample,
    kfbslide_read_region,
    kfbslide_read_roi_region,
    kfbslide_get_associated_image_names,
    kfbslide_get_associated_image_dimensions,
    kfbslide_read_associated_image,
    detect_vendor
)

__all__ = [
    'KFBSlide',
    'KFBSlideError',
    'kfbslide_open',
    'kfbslide_close', 
    'kfbslide_get_level_count',
    'kfbslide_get_level_dimensions',
    'kfbslide_get_level_downsample',
    'kfbslide_read_region',
    'kfbslide_read_roi_region',
    'kfbslide_get_associated_image_names',
    'kfbslide_get_associated_image_dimensions',
    'kfbslide_read_associated_image',
    'detect_vendor'
]

__version__ = '1.0.0'
__author__ = 'AiLabel Backend Team'
__description__ = 'KFB format image processing module for digital pathology'
